/**
 * OKX Jasper Vault 任务自动化脚本
 * 基于提取的Twitter Token自动完成所有任务
 */

class OKXJasperVaultBot {
    constructor(twitterTokens, walletAddress) {
        // Twitter认证信息
        this.twitterAuth = {
            authToken: twitterTokens.cookies?.auth_token || null,
            csrfToken: twitterTokens.cookies?.ct0 || null,
            userId: this.extractUserId(twitterTokens.cookies?.twid),
            screenName: this.extractScreenName(twitterTokens.userInfo)
        };
        
        // OKX配置
        this.walletAddress = walletAddress;
        this.giveawayId = 'jaspervault';
        this.chainId = 8453; // Base链
        this.baseUrl = 'https://web3.okx.com/priapi/v1/dapp';
        
        // 任务状态
        this.tasks = [];
        this.completedTasks = [];
        
        console.log('🚀 OKX Jasper Vault Bot 初始化完成');
        console.log('🐦 Twitter用户:', this.twitterAuth.screenName || this.twitterAuth.userId);
        console.log('💰 钱包地址:', this.walletAddress);
    }
    
    /**
     * 从twid提取用户ID
     */
    extractUserId(twid) {
        if (!twid) return null;
        const match = twid.match(/u%3D(\d+)/);
        return match ? match[1] : null;
    }
    
    /**
     * 从用户信息提取用户名
     */
    extractScreenName(userInfo) {
        if (!userInfo) return null;
        
        // 尝试从各种方法中提取用户名
        for (const key in userInfo) {
            const value = userInfo[key];
            if (typeof value === 'string') {
                // 匹配 @username 格式
                const match = value.match(/@([a-zA-Z0-9_]+)/);
                if (match) return match[1];
                
                // 匹配 Follow @username 格式
                const followMatch = value.match(/Follow @([a-zA-Z0-9_]+)/);
                if (followMatch) return followMatch[1];
            }
        }
        
        return null;
    }
    
    /**
     * 生成OKX请求头
     */
    generateHeaders(timestamp = Date.now()) {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Ok-Timestamp': timestamp.toString(),
            'Ok-Verify-Sign': this.generateVerifySign(timestamp),
            'Ok-Verify-Token': this.generateVerifyToken(),
            'X-FpToken': this.generateFpToken(),
            'X-FpToken-Signature': this.generateFpTokenSignature(),
            'X-Id-Group': this.generateIdGroup(),
            'X-Request-Timestamp': timestamp.toString(),
            'Device-Token': this.generateDeviceToken(),
            'X-Locale': 'zh_CN',
            'X-Utc': '8',
            'User-Agent': navigator.userAgent,
            'Referer': `https://web3.okx.com/zh-hans/giveaway/${this.giveawayId}`
        };
    }
    
    /**
     * 主要执行函数
     */
    async execute() {
        console.log('🎯 开始执行OKX Jasper Vault任务自动化...');
        
        try {
            // 1. 检查Twitter认证
            if (!this.validateTwitterAuth()) {
                throw new Error('Twitter认证信息不完整');
            }
            
            // 2. 连接钱包（如果未连接）
            await this.connectWallet();
            
            // 3. 绑定Twitter账号
            await this.bindTwitterAccount();
            
            // 4. 获取任务列表
            await this.fetchTasks();
            
            // 5. 执行所有任务
            await this.executeAllTasks();
            
            // 6. 验证任务完成
            await this.verifyTaskCompletion();
            
            // 7. 完成活动参与
            await this.completeParticipation();
            
            console.log('🎉 所有任务执行完成！');
            return { success: true, message: '任务自动化执行成功' };
            
        } catch (error) {
            console.error('❌ 执行失败:', error);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 验证Twitter认证信息
     */
    validateTwitterAuth() {
        const required = ['csrfToken', 'userId'];
        const missing = required.filter(field => !this.twitterAuth[field]);
        
        if (missing.length > 0) {
            console.error('❌ 缺少Twitter认证信息:', missing);
            return false;
        }
        
        console.log('✅ Twitter认证信息验证通过');
        return true;
    }
    
    /**
     * 连接钱包
     */
    async connectWallet() {
        console.log('💰 检查钱包连接状态...');
        
        // 检查页面是否已显示钱包地址
        const walletElements = document.querySelectorAll('button');
        let isConnected = false;
        
        for (const btn of walletElements) {
            if (btn.textContent?.includes('0x') && btn.textContent.length > 10) {
                isConnected = true;
                console.log('✅ 钱包已连接:', btn.textContent.trim());
                break;
            }
        }
        
        if (!isConnected) {
            console.log('🔗 尝试连接钱包...');
            
            // 查找连接钱包按钮
            const connectButtons = Array.from(document.querySelectorAll('button'))
                .filter(btn => btn.textContent?.includes('连接') || btn.textContent?.includes('Connect'));
            
            if (connectButtons.length > 0) {
                connectButtons[0].click();
                console.log('👆 已点击连接钱包按钮');
                
                // 等待连接完成
                await this.delay(3000);
            }
        }
    }
    
    /**
     * 绑定Twitter账号
     */
    async bindTwitterAccount() {
        console.log('🐦 开始Twitter账号绑定...');
        
        try {
            // 方案1: 直接API绑定
            const result = await this.directTwitterBinding();
            
            if (result.success) {
                console.log('✅ Twitter绑定成功');
                return result;
            }
            
            // 方案2: 模拟OAuth流程
            console.log('🔄 尝试备用绑定方案...');
            return await this.simulateTwitterOAuth();
            
        } catch (error) {
            console.error('❌ Twitter绑定失败:', error);
            
            // 方案3: 页面操作绑定
            console.log('🔄 尝试页面操作绑定...');
            return await this.pageTwitterBinding();
        }
    }
    
    /**
     * 直接Twitter绑定
     */
    async directTwitterBinding() {
        const timestamp = Date.now();
        
        const payload = {
            platform: 1, // Twitter
            authToken: this.twitterAuth.authToken,
            userId: this.twitterAuth.userId,
            screenName: this.twitterAuth.screenName,
            csrfToken: this.twitterAuth.csrfToken,
            walletAddress: this.walletAddress,
            bizType: 1,
            bizRequestData: {
                giveawayId: this.giveawayId,
                chainId: this.chainId
            },
            domain: 'web3.okx.com'
        };
        
        try {
            const response = await fetch(`${this.baseUrl}/oauth2/direct-bind`, {
                method: 'POST',
                headers: this.generateHeaders(timestamp),
                body: JSON.stringify(payload)
            });
            
            const result = await response.json();
            console.log('📡 直接绑定响应:', result);
            
            return {
                success: result.code === 0,
                data: result
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 页面操作绑定Twitter
     */
    async pageTwitterBinding() {
        console.log('👆 尝试页面操作绑定Twitter...');
        
        // 查找Twitter连接按钮
        const twitterButtons = Array.from(document.querySelectorAll('button'))
            .filter(btn => {
                const text = btn.textContent || '';
                return text.includes('连接') || text.includes('Twitter') || text.includes('X');
            });
        
        if (twitterButtons.length > 0) {
            console.log('👆 点击Twitter连接按钮...');
            twitterButtons[0].click();
            
            // 等待处理
            await this.delay(2000);
            
            return { success: true, method: 'page_operation' };
        }
        
        return { success: false, error: 'No Twitter connect button found' };
    }
    
    /**
     * 获取任务列表
     */
    async fetchTasks() {
        console.log('📋 获取任务列表...');
        
        try {
            const timestamp = Date.now();
            const response = await fetch(`${this.baseUrl}/giveaway/tasks?giveawayId=${this.giveawayId}&t=${timestamp}`, {
                method: 'GET',
                headers: this.generateHeaders(timestamp)
            });
            
            const result = await response.json();
            
            if (result.code === 0 && result.data) {
                this.tasks = result.data.tasks || [];
                console.log(`✅ 获取到 ${this.tasks.length} 个任务`);
                
                this.tasks.forEach((task, index) => {
                    console.log(`任务 ${index + 1}: ${task.name || task.description}`);
                });
                
                return this.tasks;
            } else {
                // 从页面DOM获取任务信息
                return this.getTasksFromDOM();
            }
        } catch (error) {
            console.log('⚠️ API获取任务失败，从页面获取...');
            return this.getTasksFromDOM();
        }
    }
    
    /**
     * 从DOM获取任务信息
     */
    getTasksFromDOM() {
        const taskElements = document.querySelectorAll('[class*="task"]');
        const tasks = [];
        
        taskElements.forEach((el, index) => {
            const text = el.textContent?.trim() || '';
            const isCompleted = text.includes('已完成') || text.includes('完成');
            const isTwitterTask = text.includes('Twitter') || text.includes('X') || text.includes('关注');
            
            tasks.push({
                id: index,
                name: text.substring(0, 100),
                type: isTwitterTask ? 'twitter' : 'other',
                completed: isCompleted,
                element: el
            });
        });
        
        this.tasks = tasks;
        console.log(`✅ 从页面获取到 ${tasks.length} 个任务`);
        return tasks;
    }
    
    /**
     * 执行所有任务
     */
    async executeAllTasks() {
        console.log('🎯 开始执行所有任务...');
        
        for (let i = 0; i < this.tasks.length; i++) {
            const task = this.tasks[i];
            
            if (task.completed) {
                console.log(`✅ 任务 ${i + 1} 已完成: ${task.name}`);
                continue;
            }
            
            console.log(`🔄 执行任务 ${i + 1}: ${task.name}`);
            
            try {
                const result = await this.executeTask(task);
                
                if (result.success) {
                    console.log(`✅ 任务 ${i + 1} 执行成功`);
                    this.completedTasks.push(task);
                } else {
                    console.log(`❌ 任务 ${i + 1} 执行失败:`, result.error);
                }
                
                // 任务间延迟
                await this.delay(2000);
                
            } catch (error) {
                console.error(`❌ 任务 ${i + 1} 执行异常:`, error);
            }
        }
        
        console.log(`🎉 任务执行完成，成功: ${this.completedTasks.length}/${this.tasks.length}`);
    }
    
    /**
     * 执行单个任务
     */
    async executeTask(task) {
        // 如果任务有对应的DOM元素，尝试点击
        if (task.element) {
            try {
                // 查找任务元素中的按钮
                const buttons = task.element.querySelectorAll('button');
                
                for (const btn of buttons) {
                    if (!btn.disabled && btn.textContent?.trim()) {
                        console.log(`👆 点击任务按钮: ${btn.textContent.trim()}`);
                        btn.click();
                        await this.delay(1000);
                        
                        return { success: true, method: 'dom_click' };
                    }
                }
            } catch (error) {
                console.log('⚠️ DOM操作失败:', error);
            }
        }
        
        // API方式执行任务
        return await this.executeTaskAPI(task);
    }
    
    /**
     * 通过API执行任务
     */
    async executeTaskAPI(task) {
        const timestamp = Date.now();
        
        const payload = {
            taskId: task.id,
            giveawayId: this.giveawayId,
            walletAddress: this.walletAddress,
            chainId: this.chainId
        };
        
        try {
            const response = await fetch(`${this.baseUrl}/giveaway/task/complete`, {
                method: 'POST',
                headers: this.generateHeaders(timestamp),
                body: JSON.stringify(payload)
            });
            
            const result = await response.json();
            
            return {
                success: result.code === 0,
                data: result
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 验证任务完成
     */
    async verifyTaskCompletion() {
        console.log('🔍 验证任务完成状态...');
        
        // 查找验证按钮
        const verifyButtons = Array.from(document.querySelectorAll('button'))
            .filter(btn => btn.textContent?.includes('验证'));
        
        if (verifyButtons.length > 0) {
            console.log('👆 点击验证按钮...');
            verifyButtons[0].click();
            await this.delay(3000);
        }
        
        return { success: true };
    }
    
    /**
     * 完成活动参与
     */
    async completeParticipation() {
        console.log('🎯 完成活动参与...');
        
        // 查找参与活动按钮
        const participateButtons = Array.from(document.querySelectorAll('button'))
            .filter(btn => btn.textContent?.includes('参与'));
        
        if (participateButtons.length > 0) {
            console.log('👆 点击参与活动按钮...');
            participateButtons[0].click();
            await this.delay(2000);
        }
        
        return { success: true };
    }
    
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 以下是签名生成函数（需要根据实际情况调整）
    generateVerifySign(timestamp) {
        return 'D7d3aWXr/pUdhKwRd0G0CgF4q2c2nUuvbC+rKH2frRc=';
    }
    
    generateVerifyToken() {
        return '6949b459-de9d-4953-a692-993a02e39aad';
    }
    
    generateFpToken() {
        return '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    }
    
    generateFpTokenSignature() {
        return Math.random().toString(36).substring(2, 15);
    }
    
    generateIdGroup() {
        return '2140143310648640001-c-42';
    }
    
    generateDeviceToken() {
        return '55339c33-d6d7-4bbb-ab6a-83870d73f080';
    }
}

// 使用示例和快速启动函数
console.log('🚀 OKX Jasper Vault 自动化脚本已加载');

// 快速启动函数
window.startOKXAutomation = async function(walletAddress) {
    console.log('🎯 启动OKX自动化...');
    
    // 使用之前提取的Twitter tokens
    const twitterTokens = {
        cookies: {
            ct0: "3d339fd2a7aded55f9b891bdeceaee6d7ad5370ed6b791afcdff7e3044aed7be5c79068943a6a80241843c71dca1bf0b38a3626658d58142922605f2534d79218f5d2a6b19d7bbd72d76bec4dac963cb",
            twid: "u%3D709003675"
        },
        userInfo: {
            method_3: "Follow @dsqqzx"
        }
    };
    
    const bot = new OKXJasperVaultBot(twitterTokens, walletAddress);
    const result = await bot.execute();
    
    if (result.success) {
        console.log('🎉 自动化执行成功！');
    } else {
        console.log('❌ 自动化执行失败:', result.error);
    }
    
    return result;
};

console.log('📖 使用方法:');
console.log('  await startOKXAutomation("0x你的钱包地址");');
