/**
 * 钱包连接测试脚本
 * 用于测试新钱包的连接功能
 */

const { HeadlessWalletConnect } = require('./headless_wallet_connect');
const fs = require('fs');

class WalletTester {
    constructor() {
        this.results = [];
    }

    /**
     * 测试单个钱包连接
     */
    async testSingleWallet(privateKey, address, testName = 'Test Wallet') {
        console.log(`\n🧪 开始测试: ${testName}`);
        console.log(`📍 钱包地址: ${address}`);
        
        const connector = new HeadlessWalletConnect({
            headless: false, // 测试时显示浏览器
            devtools: true,
            slowMo: 200
        });

        const startTime = Date.now();
        let result = {
            testName,
            address,
            success: false,
            error: null,
            duration: 0,
            timestamp: new Date().toISOString()
        };

        try {
            // 执行连接测试
            const connectionInfo = await connector.connectWallet(
                privateKey,
                address,
                'https://web3.okx.com/zh-hans/giveaway/jaspervault'
            );

            result.success = true;
            result.connectionInfo = connectionInfo;
            result.duration = Date.now() - startTime;

            console.log(`✅ 测试成功: ${testName}`);
            console.log(`⏱️  耗时: ${result.duration}ms`);
            console.log(`📊 连接信息:`, connectionInfo);

            // 截图保存
            await connector.screenshot(`test_${testName.replace(/\s+/g, '_')}_success.png`);

            // 等待一段时间观察结果
            console.log('⏳ 等待5秒观察结果...');
            await new Promise(resolve => setTimeout(resolve, 5000));

        } catch (error) {
            result.success = false;
            result.error = error.message;
            result.duration = Date.now() - startTime;

            console.error(`❌ 测试失败: ${testName}`);
            console.error(`🐛 错误信息: ${error.message}`);

            // 错误截图
            await connector.screenshot(`test_${testName.replace(/\s+/g, '_')}_error.png`);

        } finally {
            await connector.close();
            this.results.push(result);
        }

        return result;
    }

    /**
     * 批量测试多个钱包
     */
    async testMultipleWallets(wallets) {
        console.log(`🚀 开始批量测试 ${wallets.length} 个钱包...\n`);

        for (let i = 0; i < wallets.length; i++) {
            const wallet = wallets[i];
            console.log(`\n📋 进度: ${i + 1}/${wallets.length}`);
            
            await this.testSingleWallet(
                wallet.privateKey,
                wallet.address,
                wallet.name || `钱包${i + 1}`
            );

            // 钱包之间的延迟
            if (i < wallets.length - 1) {
                console.log('⏸️  等待3秒后测试下一个钱包...');
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }

        this.generateReport();
    }

    /**
     * 生成测试报告
     */
    generateReport() {
        console.log('\n📊 ===== 测试报告 =====');
        
        const successCount = this.results.filter(r => r.success).length;
        const failCount = this.results.length - successCount;
        const avgDuration = this.results.reduce((sum, r) => sum + r.duration, 0) / this.results.length;

        console.log(`✅ 成功: ${successCount}/${this.results.length}`);
        console.log(`❌ 失败: ${failCount}/${this.results.length}`);
        console.log(`⏱️  平均耗时: ${Math.round(avgDuration)}ms`);

        console.log('\n📝 详细结果:');
        this.results.forEach((result, index) => {
            const status = result.success ? '✅' : '❌';
            console.log(`${index + 1}. ${status} ${result.testName} (${result.duration}ms)`);
            if (!result.success) {
                console.log(`   错误: ${result.error}`);
            }
        });

        // 保存报告到文件
        const reportData = {
            summary: {
                total: this.results.length,
                success: successCount,
                failed: failCount,
                successRate: `${Math.round(successCount / this.results.length * 100)}%`,
                averageDuration: `${Math.round(avgDuration)}ms`
            },
            details: this.results,
            generatedAt: new Date().toISOString()
        };

        fs.writeFileSync('wallet_test_report.json', JSON.stringify(reportData, null, 2));
        console.log('\n💾 测试报告已保存到: wallet_test_report.json');
    }

    /**
     * 验证钱包地址格式
     */
    validateWalletAddress(address) {
        const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/;
        return ethAddressRegex.test(address);
    }

    /**
     * 验证私钥格式
     */
    validatePrivateKey(privateKey) {
        const privateKeyRegex = /^(0x)?[a-fA-F0-9]{64}$/;
        return privateKeyRegex.test(privateKey);
    }
}

// 测试用例
async function runTests() {
    const tester = new WalletTester();

    // 示例钱包配置 - 请替换为你的实际钱包信息
    const testWallets = [
        {
            name: '测试钱包1',
            privateKey: 'YOUR_PRIVATE_KEY_1', // 替换为实际私钥
            address: 'YOUR_WALLET_ADDRESS_1'   // 替换为实际地址
        },
        {
            name: '测试钱包2', 
            privateKey: 'YOUR_PRIVATE_KEY_2', // 替换为实际私钥
            address: 'YOUR_WALLET_ADDRESS_2'   // 替换为实际地址
        }
    ];

    // 验证钱包信息
    console.log('🔍 验证钱包信息...');
    for (const wallet of testWallets) {
        if (!tester.validateWalletAddress(wallet.address)) {
            console.error(`❌ 无效的钱包地址: ${wallet.address}`);
            return;
        }
        if (!tester.validatePrivateKey(wallet.privateKey)) {
            console.error(`❌ 无效的私钥格式: ${wallet.name}`);
            return;
        }
    }

    console.log('✅ 钱包信息验证通过');

    // 检查是否有实际的钱包信息
    if (testWallets.some(w => w.privateKey.includes('YOUR_PRIVATE_KEY'))) {
        console.log('\n⚠️  请先在脚本中配置实际的钱包私钥和地址！');
        console.log('📝 编辑 test_wallet_connect.js 文件，替换 YOUR_PRIVATE_KEY 和 YOUR_WALLET_ADDRESS');
        return;
    }

    // 执行测试
    try {
        await tester.testMultipleWallets(testWallets);
    } catch (error) {
        console.error('❌ 测试执行失败:', error);
    }
}

// 单独测试一个钱包的函数
async function testSingleWalletQuick(privateKey, address) {
    const tester = new WalletTester();
    
    if (!tester.validateWalletAddress(address)) {
        console.error('❌ 无效的钱包地址格式');
        return;
    }
    
    if (!tester.validatePrivateKey(privateKey)) {
        console.error('❌ 无效的私钥格式');
        return;
    }
    
    await tester.testSingleWallet(privateKey, address, '快速测试');
}

// 导出函数
module.exports = {
    WalletTester,
    runTests,
    testSingleWalletQuick
};

// 如果直接运行此文件
if (require.main === module) {
    console.log('🧪 OKX钱包连接测试工具');
    console.log('📋 使用方法:');
    console.log('1. 编辑脚本中的钱包信息');
    console.log('2. 运行: node test_wallet_connect.js');
    console.log('3. 查看测试结果和截图\n');
    
    runTests().catch(console.error);
}
