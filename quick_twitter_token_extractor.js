/**
 * 快速Twitter AuthToken提取器
 * 直接在Twitter页面控制台中运行
 */

(function() {
    console.log('🔍 开始快速提取Twitter AuthToken...');
    
    const tokens = {
        authToken: null,
        csrfToken: null,
        bearerToken: null,
        userId: null,
        screenName: null,
        userInfo: {}
    };

    // 1. 从Cookies提取主要令牌
    console.log('🍪 检查Cookies...');
    const cookies = document.cookie.split(';');
    cookies.forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        switch(name) {
            case 'auth_token':
                tokens.authToken = value;
                console.log('  ✅ 找到 auth_token:', value?.substring(0, 20) + '...');
                break;
            case 'ct0':
                tokens.csrfToken = value;
                console.log('  ✅ 找到 ct0 (CSRF):', value?.substring(0, 20) + '...');
                break;
            case 'twid':
                tokens.userId = value?.replace(/^u%3D/, '');
                console.log('  ✅ 找到 userId:', tokens.userId);
                break;
        }
    });

    // 2. 从页面DOM提取用户信息
    console.log('👤 提取用户信息...');
    try {
        // 方法1: 从URL提取用户名
        const urlMatch = window.location.pathname.match(/\/([^\/]+)$/);
        if (urlMatch && urlMatch[1] !== 'home' && urlMatch[1] !== 'notifications') {
            tokens.screenName = urlMatch[1];
            console.log('  ✅ 从URL获取用户名:', tokens.screenName);
        }

        // 方法2: 从页面元素提取
        const userNameElement = document.querySelector('[data-testid="UserName"]');
        if (userNameElement) {
            tokens.userInfo.displayName = userNameElement.textContent;
            console.log('  ✅ 显示名称:', tokens.userInfo.displayName);
        }

        const screenNameElement = document.querySelector('[data-testid="UserScreenName"]');
        if (screenNameElement) {
            const screenName = screenNameElement.textContent.replace('@', '');
            if (!tokens.screenName) tokens.screenName = screenName;
            console.log('  ✅ 用户名:', screenName);
        }

        // 方法3: 从页面标题提取
        if (!tokens.screenName && document.title.includes('(@')) {
            const titleMatch = document.title.match(/\(@([^)]+)\)/);
            if (titleMatch) {
                tokens.screenName = titleMatch[1];
                console.log('  ✅ 从标题获取用户名:', tokens.screenName);
            }
        }
    } catch (e) {
        console.log('  ⚠️ 提取用户信息时出错:', e.message);
    }

    // 3. 设置网络拦截器获取Bearer Token
    console.log('🌐 设置网络拦截器...');
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const [url, options] = args;
        
        if ((url.includes('api.twitter.com') || url.includes('api.x.com')) && options?.headers) {
            const authHeader = options.headers['authorization'] || options.headers['Authorization'];
            if (authHeader && authHeader.startsWith('Bearer ')) {
                tokens.bearerToken = authHeader.replace('Bearer ', '');
                console.log('  ✅ 拦截到 Bearer Token:', tokens.bearerToken.substring(0, 30) + '...');
            }
        }
        
        return originalFetch.apply(this, args);
    };

    // 4. 尝试触发API请求获取Bearer Token
    console.log('🔄 尝试触发API请求...');
    setTimeout(() => {
        // 模拟一些可能触发API请求的操作
        try {
            // 触发时间线刷新
            const refreshButton = document.querySelector('[aria-label*="刷新"]') || 
                                document.querySelector('[aria-label*="Refresh"]');
            if (refreshButton) {
                refreshButton.click();
                console.log('  ✅ 触发了刷新操作');
            }
        } catch (e) {
            console.log('  ⚠️ 无法触发刷新操作');
        }
    }, 1000);

    // 5. 生成结果
    setTimeout(() => {
        console.log('\n📊 提取结果汇总:');
        console.log('==========================================');
        
        if (tokens.authToken) {
            console.log('✅ Auth Token:', tokens.authToken);
        } else {
            console.log('❌ Auth Token: 未找到');
        }
        
        if (tokens.csrfToken) {
            console.log('✅ CSRF Token:', tokens.csrfToken);
        } else {
            console.log('❌ CSRF Token: 未找到');
        }
        
        if (tokens.bearerToken) {
            console.log('✅ Bearer Token:', tokens.bearerToken.substring(0, 50) + '...');
        } else {
            console.log('❌ Bearer Token: 未找到 (可能需要等待API请求)');
        }
        
        if (tokens.userId) {
            console.log('✅ User ID:', tokens.userId);
        } else {
            console.log('❌ User ID: 未找到');
        }
        
        if (tokens.screenName) {
            console.log('✅ Screen Name:', tokens.screenName);
        } else {
            console.log('❌ Screen Name: 未找到');
        }

        console.log('\n🎯 可用于OKX绑定的信息:');
        console.log('==========================================');
        
        if (tokens.authToken && tokens.screenName) {
            console.log('✅ 具备基本绑定条件');
            console.log('📋 复制以下信息用于绑定:');
            console.log(`authToken: "${tokens.authToken}"`);
            console.log(`screenName: "${tokens.screenName}"`);
            if (tokens.userId) console.log(`userId: "${tokens.userId}"`);
            
            // 生成绑定代码
            console.log('\n🚀 OKX绑定代码:');
            console.log('==========================================');
            console.log(`
const bindingData = {
    authToken: "${tokens.authToken}",
    screenName: "${tokens.screenName}",
    userId: "${tokens.userId || 'unknown'}",
    csrfToken: "${tokens.csrfToken || ''}"
};

// 使用这些数据调用OKX绑定API
console.log('绑定数据:', bindingData);
            `);
        } else {
            console.log('❌ 缺少必要信息，无法进行绑定');
            console.log('💡 建议:');
            if (!tokens.authToken) console.log('   - 确保已登录Twitter');
            if (!tokens.screenName) console.log('   - 访问你的Twitter个人资料页面');
        }

        // 将结果保存到全局变量
        window.twitterTokens = tokens;
        console.log('\n💾 结果已保存到 window.twitterTokens');
        
    }, 3000);

    console.log('⏳ 正在提取中，请等待3秒...');
    
})();

// 额外的辅助函数
window.exportTwitterTokens = function() {
    if (window.twitterTokens) {
        const data = {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            tokens: window.twitterTokens
        };
        
        console.log('📁 导出的Token数据:', JSON.stringify(data, null, 2));
        
        // 复制到剪贴板
        navigator.clipboard.writeText(JSON.stringify(data, null, 2)).then(() => {
            console.log('✅ 数据已复制到剪贴板');
        }).catch(() => {
            console.log('⚠️ 无法复制到剪贴板，请手动复制上面的数据');
        });
    } else {
        console.log('❌ 没有找到Token数据，请先运行提取脚本');
    }
};

console.log('\n📖 使用说明:');
console.log('1. 确保已登录Twitter');
console.log('2. 在Twitter页面运行此脚本');
console.log('3. 等待3秒查看结果');
console.log('4. 运行 exportTwitterTokens() 导出数据');
