/**
 * 无头浏览器钱包连接自动化脚本
 * 支持使用私钥自动连接OKX钱包
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class HeadlessWalletConnect {
    constructor(options = {}) {
        this.options = {
            headless: options.headless !== false, // 默认无头模式
            devtools: options.devtools || false,
            slowMo: options.slowMo || 100,
            timeout: options.timeout || 30000,
            ...options
        };
        this.browser = null;
        this.page = null;
        this.walletPrivateKey = null;
        this.walletAddress = null;
    }

    /**
     * 初始化浏览器
     */
    async init() {
        console.log('🚀 启动无头浏览器...');
        
        this.browser = await puppeteer.launch({
            headless: this.options.headless,
            devtools: this.options.devtools,
            slowMo: this.options.slowMo,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-extensions-except=./okx-wallet-extension', // OKX钱包扩展路径
                '--load-extension=./okx-wallet-extension'
            ]
        });

        this.page = await this.browser.newPage();
        
        // 设置视口
        await this.page.setViewport({ width: 1280, height: 720 });
        
        // 设置用户代理
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        
        console.log('✅ 浏览器初始化完成');
    }

    /**
     * 设置钱包私钥
     */
    setWalletCredentials(privateKey, address = null) {
        this.walletPrivateKey = privateKey;
        this.walletAddress = address;
        console.log('✅ 钱包凭据已设置');
    }

    /**
     * 注入钱包模拟脚本
     */
    async injectWalletSimulator() {
        console.log('🔧 注入钱包模拟器...');
        
        await this.page.evaluateOnNewDocument((privateKey, address) => {
            // 模拟OKX钱包对象
            window.okxwallet = {
                isOkxWallet: true,
                isConnected: false,
                selectedAddress: null,
                chainId: '0x2105', // Base链 (8453)
                
                // 连接钱包
                async connect() {
                    console.log('🔗 模拟钱包连接...');
                    this.isConnected = true;
                    this.selectedAddress = address;
                    
                    // 触发连接事件
                    window.dispatchEvent(new CustomEvent('okxwallet_accountsChanged', {
                        detail: [address]
                    }));
                    
                    return [address];
                },
                
                // 请求账户
                async request(args) {
                    console.log('📝 钱包请求:', args);
                    
                    switch (args.method) {
                        case 'eth_requestAccounts':
                            return await this.connect();
                        
                        case 'eth_accounts':
                            return this.isConnected ? [this.selectedAddress] : [];
                        
                        case 'eth_chainId':
                            return this.chainId;
                        
                        case 'wallet_switchEthereumChain':
                            console.log('🔄 切换链到:', args.params[0].chainId);
                            this.chainId = args.params[0].chainId;
                            return null;
                        
                        case 'personal_sign':
                        case 'eth_sign':
                            // 这里需要实际的签名逻辑
                            console.log('✍️ 签名请求:', args.params);
                            return '0x' + '0'.repeat(130); // 模拟签名
                        
                        default:
                            console.log('❓ 未知方法:', args.method);
                            return null;
                    }
                },
                
                // 监听器
                on(event, callback) {
                    window.addEventListener(`okxwallet_${event}`, callback);
                },
                
                removeListener(event, callback) {
                    window.removeEventListener(`okxwallet_${event}`, callback);
                }
            };
            
            // 同时设置为 window.ethereum (兼容性)
            window.ethereum = window.okxwallet;
            
            console.log('✅ OKX钱包模拟器已注入');
        }, this.walletPrivateKey, this.walletAddress);
    }

    /**
     * 导航到目标页面
     */
    async navigateToPage(url = 'https://web3.okx.com/zh-hans/giveaway/jaspervault') {
        console.log('🌐 导航到页面:', url);
        
        await this.page.goto(url, {
            waitUntil: 'networkidle2',
            timeout: this.options.timeout
        });
        
        console.log('✅ 页面加载完成');
    }

    /**
     * 等待并点击连接钱包按钮
     */
    async clickConnectWalletButton() {
        console.log('🔍 寻找连接钱包按钮...');
        
        // 等待页面加载完成
        await this.page.waitForTimeout(2000);
        
        // 尝试多个可能的连接钱包按钮选择器
        const buttonSelectors = [
            'button:contains("连接 OKX Wallet 并参与活动")',
            'button:contains("连接钱包")',
            '[data-testid="connect-wallet"]',
            '.connect-wallet-btn',
            'button[class*="connect"]'
        ];
        
        let buttonFound = false;
        
        for (const selector of buttonSelectors) {
            try {
                // 使用XPath查找包含文本的按钮
                if (selector.includes('contains')) {
                    const text = selector.match(/contains\("(.+)"\)/)[1];
                    const xpath = `//button[contains(text(), "${text}")]`;
                    
                    const elements = await this.page.$x(xpath);
                    if (elements.length > 0) {
                        console.log(`✅ 找到连接钱包按钮: ${text}`);
                        await elements[0].click();
                        buttonFound = true;
                        break;
                    }
                } else {
                    const element = await this.page.$(selector);
                    if (element) {
                        console.log(`✅ 找到连接钱包按钮: ${selector}`);
                        await element.click();
                        buttonFound = true;
                        break;
                    }
                }
            } catch (error) {
                console.log(`❌ 选择器 ${selector} 未找到`);
            }
        }
        
        if (!buttonFound) {
            // 如果没找到，尝试通过文本内容查找
            console.log('🔍 通过文本内容查找按钮...');
            await this.page.evaluate(() => {
                const buttons = Array.from(document.querySelectorAll('button, div[role="button"], a'));
                const connectButton = buttons.find(btn => 
                    btn.textContent.includes('连接') && 
                    (btn.textContent.includes('钱包') || btn.textContent.includes('OKX'))
                );
                
                if (connectButton) {
                    console.log('✅ 通过文本找到连接按钮');
                    connectButton.click();
                    return true;
                }
                return false;
            });
        }
        
        console.log('🔗 连接钱包按钮已点击');
        await this.page.waitForTimeout(1000);
    }

    /**
     * 处理钱包选择弹窗
     */
    async handleWalletSelection() {
        console.log('🔍 处理钱包选择弹窗...');
        
        // 等待弹窗出现
        await this.page.waitForTimeout(2000);
        
        // 查找OKX钱包选项
        const okxSelectors = [
            'div:contains("OKX Wallet")',
            'button:contains("OKX")',
            '[data-wallet="okx"]',
            '.wallet-option[data-name*="okx"]'
        ];
        
        for (const selector of okxSelectors) {
            try {
                if (selector.includes('contains')) {
                    const text = selector.match(/contains\("(.+)"\)/)[1];
                    const xpath = `//div[contains(text(), "${text}")] | //button[contains(text(), "${text}")]`;
                    
                    const elements = await this.page.$x(xpath);
                    if (elements.length > 0) {
                        console.log(`✅ 找到OKX钱包选项: ${text}`);
                        await elements[0].click();
                        break;
                    }
                }
            } catch (error) {
                console.log(`❌ OKX选择器 ${selector} 未找到`);
            }
        }
        
        console.log('✅ OKX钱包已选择');
        await this.page.waitForTimeout(1000);
    }

    /**
     * 等待钱包连接完成
     */
    async waitForConnection() {
        console.log('⏳ 等待钱包连接完成...');
        
        try {
            // 等待连接状态变化
            await this.page.waitForFunction(() => {
                return window.okxwallet && window.okxwallet.isConnected;
            }, { timeout: 10000 });
            
            console.log('✅ 钱包连接成功！');
            
            // 获取连接后的页面状态
            const connectionInfo = await this.page.evaluate(() => {
                return {
                    isConnected: window.okxwallet?.isConnected,
                    address: window.okxwallet?.selectedAddress,
                    chainId: window.okxwallet?.chainId
                };
            });
            
            console.log('📊 连接信息:', connectionInfo);
            return connectionInfo;
            
        } catch (error) {
            console.error('❌ 等待连接超时:', error);
            throw error;
        }
    }

    /**
     * 完整的连接流程
     */
    async connectWallet(privateKey, address, targetUrl) {
        try {
            console.log('🚀 开始钱包连接流程...');
            
            // 1. 初始化浏览器
            await this.init();
            
            // 2. 设置钱包凭据
            this.setWalletCredentials(privateKey, address);
            
            // 3. 注入钱包模拟器
            await this.injectWalletSimulator();
            
            // 4. 导航到目标页面
            await this.navigateToPage(targetUrl);
            
            // 5. 点击连接钱包按钮
            await this.clickConnectWalletButton();
            
            // 6. 处理钱包选择
            await this.handleWalletSelection();
            
            // 7. 等待连接完成
            const connectionInfo = await this.waitForConnection();
            
            console.log('🎉 钱包连接流程完成！');
            return connectionInfo;
            
        } catch (error) {
            console.error('❌ 钱包连接失败:', error);
            throw error;
        }
    }

    /**
     * 截图保存
     */
    async screenshot(filename = 'wallet_connect_result.png') {
        if (this.page) {
            await this.page.screenshot({ 
                path: filename, 
                fullPage: true 
            });
            console.log(`📸 截图已保存: ${filename}`);
        }
    }

    /**
     * 关闭浏览器
     */
    async close() {
        if (this.browser) {
            await this.browser.close();
            console.log('🔚 浏览器已关闭');
        }
    }
}

// 使用示例
async function main() {
    const connector = new HeadlessWalletConnect({
        headless: false, // 设置为true启用无头模式
        devtools: true   // 开发时可以打开调试工具
    });
    
    try {
        // 替换为你的钱包私钥和地址
        const privateKey = 'YOUR_PRIVATE_KEY_HERE';
        const address = 'YOUR_WALLET_ADDRESS_HERE';
        const targetUrl = 'https://web3.okx.com/zh-hans/giveaway/jaspervault';
        
        const result = await connector.connectWallet(privateKey, address, targetUrl);
        console.log('连接结果:', result);
        
        // 截图保存结果
        await connector.screenshot();
        
        // 保持浏览器打开一段时间以便观察
        await new Promise(resolve => setTimeout(resolve, 10000));
        
    } catch (error) {
        console.error('执行失败:', error);
    } finally {
        await connector.close();
    }
}

// 导出类
module.exports = { HeadlessWalletConnect, main };

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}
