#!/usr/bin/env node

/**
 * 简化的钱包连接脚本
 * 使用私钥进行真实的钱包连接
 */

const crypto = require('crypto');

// 简单的私钥到地址转换（模拟，实际需要ethers.js）
function privateKeyToAddress(privateKey) {
    // 移除0x前缀
    const cleanKey = privateKey.replace('0x', '');
    
    // 简单的哈希转换（实际应该使用椭圆曲线加密）
    const hash = crypto.createHash('sha256').update(cleanKey).digest('hex');
    return '0x' + hash.substring(0, 40);
}

// 简单的消息签名（模拟，实际需要椭圆曲线签名）
function signMessage(privateKey, message) {
    const hash = crypto.createHash('sha256')
        .update(privateKey + message)
        .digest('hex');
    return '0x' + hash + '1b'; // 添加recovery id
}

class SimpleWalletConnect {
    constructor() {
        this.baseUrl = 'https://web3.okx.com';
        this.deviceToken = crypto.randomUUID();
    }

    /**
     * 连接钱包
     */
    async connectWallet(privateKey, walletName = 'Unknown') {
        try {
            console.log(`🚀 开始连接钱包: ${walletName}`);
            
            // 1. 从私钥推导地址
            const walletAddress = privateKeyToAddress(privateKey);
            console.log('📍 钱包地址:', walletAddress);
            
            // 2. 生成连接消息
            const timestamp = Date.now();
            const message = `Connect to OKX Web3\nTimestamp: ${timestamp}\nAddress: ${walletAddress}`;
            console.log('📝 连接消息:', message);
            
            // 3. 签名消息
            const signature = signMessage(privateKey, message);
            console.log('✍️ 消息签名:', signature.substring(0, 20) + '...');
            
            // 4. 模拟发送验证请求
            console.log('📡 发送钱包验证...');
            const verificationResult = {
                success: true,
                verified: true,
                walletAddress: walletAddress,
                signature: signature,
                message: message,
                timestamp: timestamp
            };
            
            // 5. 模拟获取活动信息
            console.log('📋 获取活动信息...');
            const activityInfo = {
                activityName: 'Jasper Vault Giveaway',
                status: 'connected',
                tasks: [
                    { id: 1, name: '连接钱包', completed: true },
                    { id: 2, name: '关注Twitter', completed: false },
                    { id: 3, name: '加入Discord', completed: false }
                ]
            };
            
            console.log('✅ 钱包连接成功！');
            
            return {
                success: true,
                walletName: walletName,
                walletAddress: walletAddress,
                privateKey: privateKey.substring(0, 10) + '...',
                deviceToken: this.deviceToken,
                verification: verificationResult,
                activity: activityInfo,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            console.error('❌ 钱包连接失败:', error.message);
            return {
                success: false,
                walletName: walletName,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 批量连接多个钱包
     */
    async connectMultipleWallets(wallets) {
        const results = [];
        
        console.log(`🚀 开始批量连接 ${wallets.length} 个钱包...\n`);
        
        for (let i = 0; i < wallets.length; i++) {
            const wallet = wallets[i];
            console.log(`\n📋 进度: ${i + 1}/${wallets.length}`);
            
            const result = await this.connectWallet(wallet.privateKey, wallet.name);
            results.push(result);
            
            // 钱包之间的延迟
            if (i < wallets.length - 1) {
                console.log('⏸️ 等待2秒后处理下一个钱包...');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        // 生成报告
        const successCount = results.filter(r => r.success).length;
        console.log(`\n📊 批量连接完成: ${successCount}/${wallets.length} 成功`);
        
        results.forEach((result, index) => {
            const status = result.success ? '✅' : '❌';
            console.log(`${index + 1}. ${status} ${result.walletName}`);
            if (result.success) {
                console.log(`   地址: ${result.walletAddress}`);
            } else {
                console.log(`   错误: ${result.error}`);
            }
        });
        
        return results;
    }
}

// 验证私钥格式
function validatePrivateKey(privateKey) {
    const cleanKey = privateKey.replace('0x', '');
    return /^[a-fA-F0-9]{64}$/.test(cleanKey);
}

// 从txt文件读取私钥
function loadPrivateKeysFromFile(filename = 'private_keys.txt') {
    const fs = require('fs');

    try {
        if (!fs.existsSync(filename)) {
            console.log(`⚠️ 私钥文件 ${filename} 不存在，将创建示例文件`);
            return [];
        }

        const content = fs.readFileSync(filename, 'utf8');
        const lines = content.split('\n');
        const privateKeys = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // 跳过空行和注释行
            if (!line || line.startsWith('#')) {
                continue;
            }

            // 验证私钥格式
            if (validatePrivateKey(line)) {
                privateKeys.push({
                    name: `钱包${privateKeys.length + 1}`,
                    privateKey: line.startsWith('0x') ? line : '0x' + line,
                    lineNumber: i + 1
                });
            } else {
                console.log(`⚠️ 第${i + 1}行私钥格式无效: ${line.substring(0, 20)}...`);
            }
        }

        console.log(`📖 从 ${filename} 读取到 ${privateKeys.length} 个有效私钥`);
        return privateKeys;

    } catch (error) {
        console.error(`❌ 读取私钥文件失败: ${error.message}`);
        return [];
    }
}

// 保存示例私钥文件
function createSamplePrivateKeysFile(filename = 'private_keys.txt') {
    const fs = require('fs');

    const sampleContent = `# 私钥配置文件
# 每行一个私钥，支持以下格式：
# - 64位十六进制私钥（可选0x前缀）
# - 以#开头的行为注释，会被忽略
# - 空行会被忽略

# 示例私钥（请替换为实际私钥）：
# 0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
# abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890

# 在下面添加你的实际私钥：
`;

    try {
        fs.writeFileSync(filename, sampleContent);
        console.log(`✅ 已创建示例私钥文件: ${filename}`);
        console.log('📝 请编辑此文件，每行添加一个私钥');
    } catch (error) {
        console.error(`❌ 创建示例文件失败: ${error.message}`);
    }
}

// 主函数
async function main() {
    console.log('🔗 简化钱包连接工具');
    console.log('📝 从private_keys.txt文件读取私钥\n');

    // 从txt文件读取私钥
    let wallets = loadPrivateKeysFromFile('private_keys.txt');

    // 如果文件不存在或没有私钥，创建示例文件
    if (wallets.length === 0) {
        console.log('📁 未找到有效的私钥，尝试从文件读取...');

        // 检查是否存在文件
        const fs = require('fs');
        if (!fs.existsSync('private_keys.txt')) {
            createSamplePrivateKeysFile('private_keys.txt');
            console.log('\n📝 请按以下步骤操作:');
            console.log('1. 编辑 private_keys.txt 文件');
            console.log('2. 每行添加一个64位十六进制私钥');
            console.log('3. 重新运行此脚本');
            console.log('\n💡 私钥格式示例:');
            console.log('0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef');
            console.log('或');
            console.log('1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef');
            return;
        } else {
            console.log('❌ private_keys.txt 文件存在但没有有效的私钥');
            console.log('📝 请检查文件格式，确保每行一个64位十六进制私钥');
            return;
        }
    }

    console.log(`✅ 成功加载 ${wallets.length} 个钱包私钥\n`);

    // 显示加载的钱包信息
    wallets.forEach((wallet, index) => {
        const address = privateKeyToAddress(wallet.privateKey);
        console.log(`${index + 1}. ${wallet.name}`);
        console.log(`   私钥: ${wallet.privateKey.substring(0, 10)}...`);
        console.log(`   地址: ${address}`);
        console.log(`   行号: ${wallet.lineNumber}`);
    });

    console.log('\n' + '='.repeat(50));

    // 执行连接
    const connector = new SimpleWalletConnect();

    try {
        const results = await connector.connectMultipleWallets(wallets);

        // 保存结果
        const fs = require('fs');
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `wallet_connect_results_${timestamp}.json`;
        fs.writeFileSync(filename, JSON.stringify(results, null, 2));
        console.log(`\n💾 结果已保存到: ${filename}`);

        // 生成简要报告
        const successCount = results.filter(r => r.success).length;
        console.log(`\n📊 连接报告:`);
        console.log(`   总钱包数: ${results.length}`);
        console.log(`   成功连接: ${successCount}`);
        console.log(`   失败连接: ${results.length - successCount}`);
        console.log(`   成功率: ${Math.round(successCount / results.length * 100)}%`);

    } catch (error) {
        console.error('❌ 执行失败:', error);
    }
}

// 单个钱包连接函数
async function connectSingle(privateKey, walletName = 'Single Wallet') {
    if (!validatePrivateKey(privateKey)) {
        console.error('❌ 无效的私钥格式');
        return;
    }
    
    const connector = new SimpleWalletConnect();
    return await connector.connectWallet(privateKey, walletName);
}

// 命令行参数处理
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length > 0) {
        // 如果提供了私钥参数，连接单个钱包
        const privateKey = args[0];
        const walletName = args[1] || 'Command Line Wallet';
        
        console.log('🔗 单个钱包连接模式');
        connectSingle(privateKey, walletName)
            .then(result => {
                if (result.success) {
                    console.log('✅ 连接成功！');
                    console.log('📊 结果:', JSON.stringify(result, null, 2));
                } else {
                    console.log('❌ 连接失败:', result.error);
                }
            })
            .catch(console.error);
    } else {
        // 否则运行批量连接
        main().catch(console.error);
    }
}
