/**
 * 安装依赖脚本
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('📦 检查和安装依赖包...\n');

// 检查package.json是否存在
if (!fs.existsSync('package.json')) {
    console.log('📝 创建package.json...');
    const packageJson = {
        "name": "okx-wallet-automation",
        "version": "1.0.0",
        "description": "OKX钱包自动连接工具",
        "main": "wallet_signer_connect.js",
        "scripts": {
            "start": "node wallet_signer_connect.js",
            "test": "node test_wallet_signer.js"
        },
        "dependencies": {
            "ethers": "^6.8.0"
        },
        "keywords": ["okx", "wallet", "automation", "web3"],
        "author": "Your Name",
        "license": "MIT"
    };
    
    fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
    console.log('✅ package.json 创建完成');
}

// 安装依赖
try {
    console.log('⬇️ 安装ethers.js...');
    execSync('npm install ethers@^6.8.0', { stdio: 'inherit' });
    console.log('✅ ethers.js 安装完成');
    
    console.log('\n🎉 所有依赖安装完成！');
    console.log('\n📋 使用方法:');
    console.log('1. 编辑 wallet_signer_connect.js 中的私钥');
    console.log('2. 运行: node wallet_signer_connect.js');
    
} catch (error) {
    console.error('❌ 安装依赖失败:', error.message);
    console.log('\n💡 请手动安装:');
    console.log('npm install ethers');
}
