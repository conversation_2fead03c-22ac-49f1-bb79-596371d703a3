/**
 * 安全版Twitter AuthToken提取器
 * 降低被检测和封号风险的版本
 */

(function() {
    'use strict';
    
    console.log('🔒 安全版Twitter AuthToken提取器启动...');
    
    const safeExtractor = {
        tokens: {},
        
        // 安全延迟函数
        delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
        
        // 安全的Cookie读取
        async extractCookiesSafely() {
            console.log('🍪 安全提取Cookies...');
            
            try {
                // 添加随机延迟模拟人类行为
                await this.delay(Math.random() * 1000 + 500);
                
                const cookies = document.cookie.split(';');
                const authCookies = {};
                
                // 只提取关键的认证cookies
                const criticalCookies = ['auth_token', 'ct0', 'twid'];
                
                cookies.forEach(cookie => {
                    const [name, value] = cookie.trim().split('=');
                    if (criticalCookies.includes(name)) {
                        authCookies[name] = value;
                        console.log(`  ✓ 找到关键Cookie: ${name}`);
                    }
                });
                
                this.tokens.cookies = authCookies;
                return authCookies;
            } catch (error) {
                console.log('  ⚠️ Cookie提取失败:', error.message);
                return {};
            }
        },
        
        // 安全的用户信息提取
        async extractUserInfoSafely() {
            console.log('👤 安全提取用户信息...');
            
            try {
                await this.delay(Math.random() * 800 + 300);
                
                const userInfo = {};
                
                // 方法1: 从URL提取（最安全）
                const urlPath = window.location.pathname;
                const urlMatch = urlPath.match(/\/([^\/]+)$/);
                if (urlMatch && !['home', 'notifications', 'messages', 'explore'].includes(urlMatch[1])) {
                    userInfo.screenName = urlMatch[1];
                    console.log('  ✓ 从URL获取用户名:', userInfo.screenName);
                }
                
                // 方法2: 从页面标题提取（安全）
                if (!userInfo.screenName && document.title.includes('(@')) {
                    const titleMatch = document.title.match(/\(@([^)]+)\)/);
                    if (titleMatch) {
                        userInfo.screenName = titleMatch[1];
                        console.log('  ✓ 从标题获取用户名:', userInfo.screenName);
                    }
                }
                
                // 方法3: 谨慎的DOM查询（限制查询次数）
                if (!userInfo.screenName) {
                    await this.delay(500);
                    
                    const selectors = [
                        '[data-testid="UserScreenName"]',
                        'h1[role="heading"]'
                    ];
                    
                    for (const selector of selectors) {
                        try {
                            const element = document.querySelector(selector);
                            if (element && element.textContent) {
                                const text = element.textContent.replace('@', '');
                                if (text && text.length > 0 && text.length < 50) {
                                    userInfo.screenName = text;
                                    console.log('  ✓ 从DOM获取用户名:', text);
                                    break;
                                }
                            }
                        } catch (e) {
                            // 忽略单个查询失败
                        }
                        
                        // 每次查询后添加延迟
                        await this.delay(200);
                    }
                }
                
                this.tokens.userInfo = userInfo;
                return userInfo;
            } catch (error) {
                console.log('  ⚠️ 用户信息提取失败:', error.message);
                return {};
            }
        },
        
        // 最小化的存储检查
        async checkStorageMinimal() {
            console.log('💾 最小化存储检查...');
            
            try {
                await this.delay(Math.random() * 600 + 400);
                
                const storage = {};
                
                // 只检查最关键的几个键
                const criticalKeys = [
                    'twitter_auth',
                    'user_token',
                    'auth_data'
                ];
                
                criticalKeys.forEach(key => {
                    try {
                        const value = localStorage.getItem(key);
                        if (value) {
                            storage[key] = value;
                            console.log(`  ✓ 找到存储项: ${key}`);
                        }
                    } catch (e) {
                        // 忽略访问错误
                    }
                });
                
                this.tokens.storage = storage;
                return storage;
            } catch (error) {
                console.log('  ⚠️ 存储检查失败:', error.message);
                return {};
            }
        },
        
        // 被动网络监听（不修改原生函数）
        setupPassiveNetworkMonitor() {
            console.log('🌐 设置被动网络监听...');
            
            // 不修改原生fetch，而是监听现有的网络活动
            const networkData = {};
            
            // 监听页面中已有的网络请求结果
            try {
                // 检查是否有性能API可用
                if (window.performance && window.performance.getEntriesByType) {
                    const networkEntries = window.performance.getEntriesByType('navigation');
                    if (networkEntries.length > 0) {
                        console.log('  ✓ 检测到网络活动');
                    }
                }
            } catch (e) {
                // 忽略性能API错误
            }
            
            this.tokens.networkData = networkData;
            return networkData;
        },
        
        // 主提取函数
        async extractSafely() {
            console.log('🔍 开始安全提取...');
            
            try {
                // 1. 提取Cookies（最重要）
                await this.extractCookiesSafely();
                
                // 2. 提取用户信息
                await this.extractUserInfoSafely();
                
                // 3. 最小化存储检查
                await this.checkStorageMinimal();
                
                // 4. 被动网络监听
                this.setupPassiveNetworkMonitor();
                
                // 5. 分析结果
                const result = this.analyzeResults();
                
                console.log('✅ 安全提取完成！');
                return result;
                
            } catch (error) {
                console.error('❌ 提取过程出错:', error);
                return { success: false, error: error.message };
            }
        },
        
        // 结果分析
        analyzeResults() {
            const result = {
                success: false,
                authToken: null,
                screenName: null,
                userId: null,
                csrfToken: null,
                recommendations: []
            };
            
            // 检查关键信息
            if (this.tokens.cookies?.auth_token) {
                result.authToken = this.tokens.cookies.auth_token;
                result.success = true;
                result.recommendations.push('✅ 找到认证令牌');
            }
            
            if (this.tokens.cookies?.ct0) {
                result.csrfToken = this.tokens.cookies.ct0;
                result.recommendations.push('✅ 找到CSRF令牌');
            }
            
            if (this.tokens.cookies?.twid) {
                // 解析用户ID
                const userIdMatch = this.tokens.cookies.twid.match(/u%3D(\d+)/);
                if (userIdMatch) {
                    result.userId = userIdMatch[1];
                    result.recommendations.push('✅ 找到用户ID');
                }
            }
            
            if (this.tokens.userInfo?.screenName) {
                result.screenName = this.tokens.userInfo.screenName;
                result.recommendations.push('✅ 找到用户名');
            }
            
            // 生成使用建议
            if (result.success && result.screenName) {
                result.recommendations.push('🎯 具备OKX绑定的基本条件');
                result.recommendations.push('💡 建议立即使用，令牌可能会过期');
            } else {
                result.recommendations.push('⚠️ 缺少关键信息，请确保已登录Twitter');
            }
            
            return result;
        },
        
        // 安全导出
        exportSafely() {
            const exportData = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                tokens: this.tokens
            };
            
            console.log('📋 提取的数据:');
            console.log(JSON.stringify(exportData, null, 2));
            
            // 尝试复制到剪贴板
            if (navigator.clipboard) {
                navigator.clipboard.writeText(JSON.stringify(exportData, null, 2))
                    .then(() => console.log('✅ 数据已复制到剪贴板'))
                    .catch(() => console.log('⚠️ 无法复制到剪贴板'));
            }
            
            return exportData;
        }
    };
    
    // 执行安全提取
    safeExtractor.extractSafely().then(result => {
        console.log('\n📊 提取结果:');
        console.log('==========================================');
        
        if (result.success) {
            console.log('✅ 提取成功！');
            console.log(`🔑 AuthToken: ${result.authToken ? '已获取' : '未找到'}`);
            console.log(`👤 用户名: ${result.screenName || '未找到'}`);
            console.log(`🆔 用户ID: ${result.userId || '未找到'}`);
            console.log(`🛡️ CSRF令牌: ${result.csrfToken ? '已获取' : '未找到'}`);
            
            if (result.authToken && result.screenName) {
                console.log('\n🚀 OKX绑定代码:');
                console.log('==========================================');
                console.log(`
// 复制以下代码到OKX页面使用
const twitterData = {
    authToken: "${result.authToken}",
    screenName: "${result.screenName}",
    userId: "${result.userId || ''}",
    csrfToken: "${result.csrfToken || ''}"
};

// 使用快速绑定函数
// await quickBindTwitter(twitterData.authToken, twitterData.screenName, "0x你的钱包地址");
                `);
            }
        } else {
            console.log('❌ 提取失败');
            console.log('💡 建议: 确保已登录Twitter并访问个人资料页面');
        }
        
        result.recommendations.forEach(rec => console.log(rec));
        
        // 保存到全局变量
        window.safeTwitterTokens = result;
        console.log('\n💾 结果已保存到 window.safeTwitterTokens');
        
        // 自动导出
        safeExtractor.exportSafely();
    });
    
    console.log('⏳ 正在安全提取中，请等待...');
    
})();

// 安全使用提示
console.log('\n🔒 安全提示:');
console.log('1. 此脚本已优化以降低检测风险');
console.log('2. 添加了随机延迟模拟人类行为');
console.log('3. 最小化DOM查询和网络拦截');
console.log('4. 建议在私人环境中使用');
console.log('5. 提取后立即使用，避免长时间存储令牌');
