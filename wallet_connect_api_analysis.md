# OKX钱包连接API分析报告

## 📋 概述
本文档分析了OKX Web3钱包连接Jasper Vault活动时的API调用流程，基于实际网络监控数据。

## 🔗 核心API端点

### 1. 获取活动详情
**端点:** `POST /priapi/v1/dapp/giveaway/getDetailV2`

**请求示例:**
```json
{
  "navName": "jaspervault",
  "walletAddress": {
    "8453": "******************************************"
  }
}
```

**响应数据包含:**
- 活动基本信息（ID、名称、奖励等）
- 任务列表
- 用户参与状态
- 链ID和钱包地址验证

### 2. 检查任务状态
**端点:** `POST /priapi/v1/dapp/giveaway/task/checkAll`

**请求示例:**
```json
{
  "giveawayId": 389,
  "walletAddress": {
    "8453": "******************************************"
  },
  "walletAccountId": "0BA4B371-174E-46FE-B2A4-033A2B3CEFE3",
  "userUniqueId": ""
}
```

**响应示例:**
```json
{
  "code": 0,
  "data": {
    "620": {"status": 1},
    "621": {"status": 1},
    "622": {"status": 1},
    "618": {"status": 1},
    "619": {"status": 1}
  }
}
```

### 3. Gas费用追踪
**端点:** `GET /priapi/v1/wallet/gas/tracker/multi-gas-price`

**参数:**
- `chainId=1` (以太坊主网)
- `t={timestamp}` (时间戳)

**用途:** 实时获取Gas费用信息

### 4. 事件追踪
**端点:** `POST /amplitude/2/httpapi`

**关键事件:**
- `web_metax_header_connectwallet_click` - 连接钱包按钮点击
- `onchain_web_wallet_users` - 钱包用户连接事件

## 🔐 认证机制

### 必需请求头
```javascript
{
  "Device-Token": "55339c33-d6d7-4bbb-ab6a-83870d73f080",
  "X-FpToken": "JWT格式的指纹令牌",
  "X-FpToken-Signature": "指纹令牌签名",
  "Ok-Verify-Sign": "请求签名验证",
  "Ok-Verify-Token": "验证令牌",
  "X-Request-Timestamp": "请求时间戳"
}
```

### 指纹令牌示例
```
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

## 💰 钱包信息

### 捕获到的钱包数据
- **钱包地址:** `******************************************`
- **链ID:** `8453` (Base链)
- **钱包账户ID:** `0BA4B371-174E-46FE-B2A4-033A2B3CEFE3`
- **设备令牌:** `55339c33-d6d7-4bbb-ab6a-83870d73f080`

## 📊 任务状态说明

### 任务ID映射
- **618:** 关注 @Jaspervault 的 X
- **619:** 加入 Jasper Vault 官方 Discord 社区
- **620:** 连接 OKX Wallet 并参与活动
- **621:** 关注 @wallet 的 X
- **622:** OKX Wallet 持有至少 10 USDT 等值代币

### 状态码
- `0`: 未完成
- `1`: 已完成

## 🔄 连接流程

### 完整的钱包连接流程
1. **用户点击连接钱包按钮**
   - 触发 `web_metax_header_connectwallet_click` 事件

2. **钱包扩展程序响应**
   - 用户在弹窗中选择OKX钱包
   - 完成钱包授权

3. **获取钱包信息**
   - 获取钱包地址和账户ID
   - 生成设备指纹和令牌

4. **发送连接成功事件**
   - 触发 `onchain_web_wallet_users` 事件

5. **获取活动详情**
   - 调用 `getDetailV2` API
   - 传入钱包地址信息

6. **检查任务状态**
   - 调用 `checkAll` API
   - 验证各项任务完成情况

## ⚠️ 安全注意事项

### 签名验证
- `Ok-Verify-Sign` 需要根据请求内容生成
- 签名算法可能包含时间戳、请求体等参数
- 需要逆向工程或官方文档获取签名方法

### 指纹令牌
- `X-FpToken` 是JWT格式，包含设备指纹信息
- 令牌有时效性，需要定期更新
- 签名使用ES256算法

### 设备识别
- `Device-Token` 用于设备唯一标识
- 与浏览器指纹、IP地址等关联
- 频繁更换可能触发风控

## 🛠️ 自动化实现建议

### 1. 环境准备
- 使用真实浏览器环境（Puppeteer/Playwright）
- 保持一致的User-Agent和设备指纹
- 模拟真实的用户行为时间间隔

### 2. 认证处理
- 实现指纹令牌生成算法
- 处理签名验证逻辑
- 管理令牌的生命周期

### 3. 错误处理
- 处理网络超时和重试
- 应对反爬虫机制
- 监控API响应状态

### 4. 数据管理
- 安全存储钱包私钥
- 记录操作日志
- 备份重要配置

## 📝 使用示例

参考 `wallet_connect_automation.js` 文件中的实现示例。

## 🔍 进一步分析

如需更深入的分析，建议：
1. 抓取更多不同场景的API调用
2. 分析签名算法的具体实现
3. 研究指纹令牌的生成机制
4. 测试不同钱包的连接流程
