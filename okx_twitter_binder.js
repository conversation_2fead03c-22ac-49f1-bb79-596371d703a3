/**
 * OKX Twitter 绑定器
 * 使用Twitter AuthToken完成OKX平台的Twitter绑定
 */

class OKXTwitterBinder {
    constructor(authToken, screenName, userId = null, csrfToken = null) {
        this.authToken = authToken;
        this.screenName = screenName;
        this.userId = userId;
        this.csrfToken = csrfToken;
        this.baseUrl = 'https://web3.okx.com/priapi/v1/dapp/oauth2';
        this.giveawayId = 389;
        this.chainId = 8453;
    }

    /**
     * 主要绑定方法
     */
    async bindTwitterAccount(walletAddress) {
        console.log('🚀 开始Twitter绑定流程...');
        console.log(`📱 钱包地址: ${walletAddress}`);
        console.log(`🐦 Twitter用户: @${this.screenName}`);

        try {
            // 方案1: 直接提交Token绑定
            console.log('\n🎯 尝试方案1: 直接Token绑定');
            const result1 = await this.directTokenBinding(walletAddress);
            if (result1.success) {
                console.log('✅ 方案1成功！');
                return result1;
            }

            // 方案2: 伪造OAuth回调
            console.log('\n🎯 尝试方案2: 伪造OAuth回调');
            const result2 = await this.fakeOAuthCallback(walletAddress);
            if (result2.success) {
                console.log('✅ 方案2成功！');
                return result2;
            }

            // 方案3: 模拟完整OAuth流程
            console.log('\n🎯 尝试方案3: 模拟OAuth流程');
            const result3 = await this.simulateOAuthFlow(walletAddress);
            if (result3.success) {
                console.log('✅ 方案3成功！');
                return result3;
            }

            console.log('❌ 所有方案都失败了');
            return { success: false, error: '所有绑定方案都失败' };

        } catch (error) {
            console.error('❌ 绑定过程中出错:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 方案1: 直接提交Token绑定
     */
    async directTokenBinding(walletAddress) {
        console.log('  📡 发送直接Token绑定请求...');
        
        const timestamp = Date.now();
        const payload = {
            platform: 1, // Twitter
            authToken: this.authToken,
            userId: this.userId,
            screenName: this.screenName,
            walletAddress: walletAddress,
            bizType: 1,
            bizRequestData: {
                giveawayId: this.giveawayId,
                chainId: this.chainId
            },
            domain: 'web3.okx.com',
            timestamp: timestamp
        };

        try {
            const response = await fetch(`${this.baseUrl}/direct-bind`, {
                method: 'POST',
                headers: this.generateHeaders(timestamp),
                body: JSON.stringify(payload)
            });

            const result = await response.json();
            console.log('  📊 响应:', result);
            
            return {
                success: result.code === 0,
                data: result,
                method: 'directTokenBinding'
            };
        } catch (error) {
            console.log('  ❌ 直接绑定失败:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 方案2: 伪造OAuth回调
     */
    async fakeOAuthCallback(walletAddress) {
        console.log('  📡 获取OAuth URL...');
        
        try {
            // 先获取OAuth URL和clientId
            const oauthResponse = await this.getOAuthUrl(walletAddress);
            if (!oauthResponse.success) {
                return { success: false, error: 'Failed to get OAuth URL' };
            }

            const { clientId } = oauthResponse.data;
            console.log('  ✅ 获取到 clientId:', clientId);

            // 生成伪造的authorization code
            const fakeCode = this.generateFakeAuthCode();
            console.log('  🔧 生成伪造授权码:', fakeCode.substring(0, 30) + '...');

            // 调用OAuth回调
            console.log('  📡 发送OAuth回调请求...');
            const timestamp = Date.now();
            const response = await fetch(`${this.baseUrl}/call-back`, {
                method: 'POST',
                headers: this.generateHeaders(timestamp),
                body: JSON.stringify({
                    code: fakeCode,
                    clientId: clientId,
                    walletAddress: walletAddress,
                    platform: 1,
                    bizType: 1,
                    bizRequestData: {
                        giveawayId: this.giveawayId,
                        chainId: this.chainId
                    },
                    domain: 'web3.okx.com',
                    // 额外的验证信息
                    userVerification: {
                        authToken: this.authToken,
                        userId: this.userId,
                        screenName: this.screenName,
                        csrfToken: this.csrfToken
                    }
                })
            });

            const result = await response.json();
            console.log('  📊 OAuth回调响应:', result);
            
            return {
                success: result.code === 0,
                data: result,
                method: 'fakeOAuthCallback'
            };
        } catch (error) {
            console.log('  ❌ OAuth回调失败:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 方案3: 模拟完整OAuth流程
     */
    async simulateOAuthFlow(walletAddress) {
        console.log('  📡 模拟完整OAuth流程...');
        
        try {
            // 1. 获取用户信息
            const userInfo = await this.getTwitterUserInfo();
            if (!userInfo) {
                return { success: false, error: 'Failed to get user info' };
            }

            // 2. 获取OAuth URL
            const oauthResponse = await this.getOAuthUrl(walletAddress);
            if (!oauthResponse.success) {
                return { success: false, error: 'Failed to get OAuth URL' };
            }

            // 3. 模拟用户授权
            const authResult = await this.simulateUserAuthorization(oauthResponse.data, userInfo);
            if (!authResult.success) {
                return { success: false, error: 'Failed to simulate authorization' };
            }

            // 4. 完成绑定
            const bindResult = await this.completeBinding(walletAddress, authResult.data);
            
            return {
                success: bindResult.success,
                data: bindResult.data,
                method: 'simulateOAuthFlow'
            };
        } catch (error) {
            console.log('  ❌ 模拟OAuth流程失败:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取OAuth URL
     */
    async getOAuthUrl(walletAddress) {
        const timestamp = Date.now();
        
        try {
            const response = await fetch(`${this.baseUrl}/oauth2-url?t=${timestamp}`, {
                method: 'POST',
                headers: this.generateHeaders(timestamp),
                body: JSON.stringify({
                    domain: 'web3.okx.com',
                    walletAddress: walletAddress,
                    platform: 1,
                    bizType: 1,
                    bizRequestData: {
                        giveawayId: this.giveawayId,
                        chainId: this.chainId
                    }
                })
            });

            const result = await response.json();
            return {
                success: result.code === 0,
                data: result.data
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 生成伪造的授权码
     */
    generateFakeAuthCode() {
        const userData = {
            userId: this.userId || this.generateFakeUserId(),
            screenName: this.screenName,
            authToken: this.authToken,
            timestamp: Date.now(),
            nonce: this.generateNonce()
        };
        
        return btoa(JSON.stringify(userData));
    }

    /**
     * 生成伪造的用户ID
     */
    generateFakeUserId() {
        return Math.floor(Math.random() * 9000000000000000000) + 1000000000000000000;
    }

    /**
     * 生成随机nonce
     */
    generateNonce() {
        return Math.random().toString(36).substring(2, 15) + 
               Math.random().toString(36).substring(2, 15);
    }

    /**
     * 获取Twitter用户信息
     */
    async getTwitterUserInfo() {
        console.log('  📡 获取Twitter用户信息...');
        
        try {
            // 尝试使用authToken调用Twitter API
            const response = await fetch('https://api.twitter.com/2/users/me', {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                console.log('  ✅ 获取到用户信息:', data);
                return data;
            } else {
                console.log('  ⚠️ Twitter API调用失败，使用本地信息');
                return {
                    data: {
                        id: this.userId || this.generateFakeUserId(),
                        username: this.screenName,
                        name: this.screenName
                    }
                };
            }
        } catch (error) {
            console.log('  ⚠️ 获取用户信息失败，使用本地信息');
            return {
                data: {
                    id: this.userId || this.generateFakeUserId(),
                    username: this.screenName,
                    name: this.screenName
                }
            };
        }
    }

    /**
     * 模拟用户授权
     */
    async simulateUserAuthorization(oauthData, userInfo) {
        console.log('  🔐 模拟用户授权...');
        
        // 生成模拟的授权响应
        const authData = {
            code: this.generateFakeAuthCode(),
            state: 'state',
            userId: userInfo.data.id,
            screenName: userInfo.data.username
        };
        
        return {
            success: true,
            data: authData
        };
    }

    /**
     * 完成绑定
     */
    async completeBinding(walletAddress, authData) {
        console.log('  ✅ 完成绑定...');
        
        const timestamp = Date.now();
        
        try {
            const response = await fetch(`${this.baseUrl}/complete-binding`, {
                method: 'POST',
                headers: this.generateHeaders(timestamp),
                body: JSON.stringify({
                    walletAddress: walletAddress,
                    authData: authData,
                    platform: 1,
                    bizType: 1,
                    bizRequestData: {
                        giveawayId: this.giveawayId,
                        chainId: this.chainId
                    },
                    domain: 'web3.okx.com'
                })
            });

            const result = await response.json();
            return {
                success: result.code === 0,
                data: result
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 生成请求头
     */
    generateHeaders(timestamp) {
        return {
            'Content-Type': 'application/json',
            'Ok-Timestamp': timestamp.toString(),
            'Ok-Verify-Sign': this.generateVerifySign(timestamp),
            'Ok-Verify-Token': this.generateVerifyToken(),
            'X-FpToken': this.generateFpToken(),
            'X-FpToken-Signature': this.generateFpTokenSignature(),
            'X-Id-Group': this.generateIdGroup(),
            'X-Request-Timestamp': timestamp.toString(),
            'Device-Token': this.generateDeviceToken(),
            'X-Locale': 'zh_CN',
            'X-Utc': '8',
            'User-Agent': navigator.userAgent,
            'Referer': 'https://web3.okx.com/zh-hans/giveaway/jaspervault'
        };
    }

    /**
     * 生成验证签名 (需要逆向分析)
     */
    generateVerifySign(timestamp) {
        // 这里需要根据实际的签名算法来实现
        // 暂时返回一个示例值
        return 'D7d3aWXr/pUdhKwRd0G0CgF4q2c2nUuvbC+rKH2frRc=';
    }

    /**
     * 生成验证令牌 (需要逆向分析)
     */
    generateVerifyToken() {
        // 这里需要根据实际的令牌生成算法来实现
        // 暂时返回一个示例值
        return '6949b459-de9d-4953-a692-993a02e39aad';
    }

    /**
     * 生成设备指纹令牌
     */
    generateFpToken() {
        return '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    }

    /**
     * 生成设备指纹签名
     */
    generateFpTokenSignature() {
        return this.generateNonce();
    }

    /**
     * 生成ID组
     */
    generateIdGroup() {
        return '2140143310648640001-c-42';
    }

    /**
     * 生成设备令牌
     */
    generateDeviceToken() {
        return '55339c33-d6d7-4bbb-ab6a-83870d73f080';
    }
}

// 使用示例
console.log('🚀 OKX Twitter 绑定器已加载');
console.log('📖 使用方法:');
console.log('  const binder = new OKXTwitterBinder(authToken, screenName, userId, csrfToken);');
console.log('  const result = await binder.bindTwitterAccount("0x你的钱包地址");');

// 快速绑定函数
window.quickBindTwitter = async function(authToken, screenName, walletAddress, userId = null, csrfToken = null) {
    console.log('🚀 开始快速Twitter绑定...');
    
    const binder = new OKXTwitterBinder(authToken, screenName, userId, csrfToken);
    const result = await binder.bindTwitterAccount(walletAddress);
    
    if (result.success) {
        console.log('🎉 绑定成功！', result);
    } else {
        console.log('❌ 绑定失败:', result.error);
    }
    
    return result;
};

console.log('\n💡 快速使用:');
console.log('  quickBindTwitter("你的authToken", "你的用户名", "0x你的钱包地址");');
