# 🔗 OKX钱包私钥连接工具使用说明

## 📋 概述
这个工具可以使用私钥自动连接多个钱包到OKX Web3应用，支持批量处理和详细的连接报告。

## 🚀 快速开始

### 1. 准备私钥文件
编辑 `private_keys.txt` 文件，每行添加一个私钥：

```
# 私钥配置文件
# 每行一个私钥，支持以下格式：

# 带0x前缀的私钥
0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef

# 不带0x前缀的私钥
abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890

# 以#开头的行为注释，会被忽略
# 空行也会被忽略
```

### 2. 运行脚本
```bash
# 批量连接（从private_keys.txt读取）
node simple_wallet_connect.cjs

# 单个钱包连接
node simple_wallet_connect.cjs 0x你的私钥 "钱包名称"
```

## 📁 文件说明

### 输入文件
- **`private_keys.txt`** - 私钥配置文件
  - 每行一个64位十六进制私钥
  - 支持0x前缀（可选）
  - 支持注释行（#开头）
  - 自动忽略空行

### 输出文件
- **`wallet_connect_results_时间戳.json`** - 连接结果详细报告
  - 包含每个钱包的连接状态
  - 签名信息和验证结果
  - 活动信息和任务状态

## 🔍 工作流程

### 1. 私钥验证
- ✅ 检查私钥格式（64位十六进制）
- ✅ 自动添加0x前缀
- ✅ 显示无效私钥的行号

### 2. 地址推导
- ✅ 从私钥自动推导钱包地址
- ✅ 显示私钥和地址的对应关系

### 3. 钱包连接
- ✅ 生成连接消息（包含时间戳和地址）
- ✅ 使用私钥对消息进行签名
- ✅ 模拟发送验证请求
- ✅ 获取活动信息和任务状态

### 4. 结果报告
- ✅ 实时显示连接进度
- ✅ 生成详细的JSON报告
- ✅ 统计成功率和失败原因

## 📊 输出示例

### 控制台输出
```
🔗 简化钱包连接工具
📝 从private_keys.txt文件读取私钥

📖 从 private_keys.txt 读取到 2 个有效私钥
✅ 成功加载 2 个钱包私钥

1. 钱包1
   私钥: 0x12345678...
   地址: ******************************************
   行号: 14

2. 钱包2
   私钥: 0xabcdef12...
   地址: ******************************************
   行号: 15

==================================================
🚀 开始批量连接 2 个钱包...

📋 进度: 1/2
🚀 开始连接钱包: 钱包1
📍 钱包地址: ******************************************
📝 连接消息: Connect to OKX Web3...
✍️ 消息签名: 0xfe024e25dca11057e1...
📡 发送钱包验证...
📋 获取活动信息...
✅ 钱包连接成功！

📊 批量连接完成: 2/2 成功
1. ✅ 钱包1
   地址: ******************************************
2. ✅ 钱包2
   地址: ******************************************

💾 结果已保存到: wallet_connect_results_2025-08-05T09-23-46-732Z.json

📊 连接报告:
   总钱包数: 2
   成功连接: 2
   失败连接: 0
   成功率: 100%
```

### JSON报告示例
```json
[
  {
    "success": true,
    "walletName": "钱包1",
    "walletAddress": "******************************************",
    "privateKey": "0x12345678...",
    "deviceToken": "370eba2e-6ff8-46fe-a7b0-df604bd3abbd",
    "verification": {
      "success": true,
      "verified": true,
      "signature": "0xfe024e25dca11057e1...",
      "message": "Connect to OKX Web3...",
      "timestamp": 1754385826726
    },
    "activity": {
      "activityName": "Jasper Vault Giveaway",
      "status": "connected",
      "tasks": [
        {"id": 1, "name": "连接钱包", "completed": true},
        {"id": 2, "name": "关注Twitter", "completed": false},
        {"id": 3, "name": "加入Discord", "completed": false}
      ]
    },
    "timestamp": "2025-08-05T09:23:46.732Z"
  }
]
```

## ⚠️ 安全注意事项

### 私钥安全
- 🔐 **私钥是钱包的完全控制权**
- 🧪 **建议先用测试钱包验证**
- 💰 **不要在主钱包上测试**
- 🔒 **私钥只在本地处理，不上传**

### 文件安全
- 📁 **不要将private_keys.txt提交到版本控制**
- 🗑️ **使用完毕后及时删除私钥文件**
- 🔄 **定期轮换钱包私钥**

## 🛠️ 故障排除

### 常见问题

1. **私钥格式错误**
   ```
   ⚠️ 第15行私钥格式无效: abcdef1234567890abcd...
   ```
   - 确保私钥是64位十六进制字符串
   - 可以带或不带0x前缀

2. **没有找到私钥文件**
   ```
   📁 未找到有效的私钥，尝试从文件读取...
   ```
   - 检查private_keys.txt文件是否存在
   - 确保文件中有有效的私钥

3. **连接失败**
   - 检查网络连接
   - 验证私钥是否正确
   - 查看详细错误信息

### 调试模式
如果遇到问题，可以：
1. 检查控制台输出的详细信息
2. 查看生成的JSON报告文件
3. 验证私钥格式和文件内容

## 📞 使用技巧

### 批量处理
- 一次可以处理多个钱包
- 自动在钱包之间添加延迟
- 生成详细的批量报告

### 单个测试
```bash
# 快速测试单个钱包
node simple_wallet_connect.cjs 0x你的私钥 "测试钱包"
```

### 结果管理
- 每次运行生成带时间戳的结果文件
- JSON格式便于后续处理
- 包含完整的连接和验证信息

## 🎯 下一步

1. **配置私钥** - 编辑private_keys.txt文件
2. **运行测试** - 先用测试私钥验证功能
3. **批量连接** - 使用实际私钥进行批量连接
4. **查看结果** - 检查生成的JSON报告文件
