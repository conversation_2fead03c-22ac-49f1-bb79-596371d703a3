/**
 * 真实钱包连接脚本 - 支持私钥签名
 * 使用ethers.js进行消息签名和钱包操作
 */

const { ethers } = require('ethers');
const crypto = require('crypto');

class WalletSignerConnect {
    constructor() {
        this.baseUrl = 'https://web3.okx.com';
        this.deviceToken = crypto.randomUUID();
        this.sessionId = Date.now().toString();
    }

    /**
     * 从私钥创建钱包实例
     */
    createWallet(privateKey) {
        try {
            // 确保私钥格式正确
            if (!privateKey.startsWith('0x')) {
                privateKey = '0x' + privateKey;
            }
            
            const wallet = new ethers.Wallet(privateKey);
            console.log('✅ 钱包实例创建成功');
            console.log('📍 钱包地址:', wallet.address);
            
            return wallet;
        } catch (error) {
            console.error('❌ 创建钱包失败:', error.message);
            throw new Error('无效的私钥格式');
        }
    }

    /**
     * 生成连接消息并签名
     */
    async signConnectMessage(wallet, timestamp = Date.now()) {
        try {
            // 生成连接消息（模拟OKX的消息格式）
            const message = `Welcome to OKX Web3!

Click to sign in and accept the OKX Web3 Terms of Service.

This request will not trigger a blockchain transaction or cost any gas fees.

Wallet address:
${wallet.address}

Nonce:
${timestamp}`;

            console.log('📝 生成连接消息:', message);
            
            // 使用私钥签名消息
            const signature = await wallet.signMessage(message);
            console.log('✍️ 消息签名完成');
            
            return {
                message,
                signature,
                address: wallet.address,
                timestamp
            };
        } catch (error) {
            console.error('❌ 签名失败:', error);
            throw error;
        }
    }

    /**
     * 验证签名（本地验证）
     */
    async verifySignature(message, signature, expectedAddress) {
        try {
            const recoveredAddress = ethers.verifyMessage(message, signature);
            const isValid = recoveredAddress.toLowerCase() === expectedAddress.toLowerCase();
            
            console.log('🔍 签名验证:', isValid ? '✅ 有效' : '❌ 无效');
            console.log('📍 恢复地址:', recoveredAddress);
            console.log('📍 期望地址:', expectedAddress);
            
            return isValid;
        } catch (error) {
            console.error('❌ 签名验证失败:', error);
            return false;
        }
    }

    /**
     * 生成请求头
     */
    generateHeaders(timestamp, extraHeaders = {}) {
        return {
            'Accept': 'application/json',
            'App-Type': 'web',
            'Content-Type': 'application/json',
            'Device-Token': this.deviceToken,
            'Devid': this.deviceToken,
            'Platform': 'web',
            'Referer': 'https://web3.okx.com/zh-hans/giveaway/jaspervault',
            'X-Cdn': 'https://web3.okx.com',
            'X-Locale': 'zh_CN',
            'X-Request-Timestamp': timestamp.toString(),
            'X-Simulated-Trading': 'undefined',
            'X-Site-Info': '==QfxojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIyUVJiOi42bpdWZyJye',
            'X-Utc': '8',
            'X-Zkdex-Env': '0',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            ...extraHeaders
        };
    }

    /**
     * 发送钱包验证请求（模拟）
     */
    async sendWalletVerification(signData) {
        const timestamp = Date.now();
        
        // 模拟发送到OKX的验证请求
        const verificationData = {
            address: signData.address,
            message: signData.message,
            signature: signData.signature,
            timestamp: signData.timestamp,
            chainId: '0x2105', // Base链
            deviceToken: this.deviceToken
        };

        console.log('📡 发送钱包验证请求...');
        console.log('🔐 验证数据:', {
            address: verificationData.address,
            signature: verificationData.signature.substring(0, 20) + '...',
            chainId: verificationData.chainId
        });

        // 这里应该发送到实际的OKX验证端点
        // 由于我们没有确切的验证API，这里模拟成功响应
        
        return {
            success: true,
            verified: true,
            walletAddress: signData.address,
            deviceToken: this.deviceToken,
            sessionToken: crypto.randomUUID(),
            message: '钱包验证成功'
        };
    }

    /**
     * 发送连接事件
     */
    async sendConnectEvent(walletAddress) {
        const url = `${this.baseUrl}/amplitude/2/httpapi`;
        const timestamp = Date.now();
        
        const eventData = {
            api_key: "56bf9d43d57f079e506b4f26c70a698f",
            events: [
                {
                    user_id: "",
                    device_id: this.deviceToken,
                    session_id: this.sessionId,
                    time: timestamp,
                    app_version: "1.10.50",
                    platform: "Web",
                    language: "zh_CN",
                    ip: "$remote",
                    insert_id: crypto.randomUUID(),
                    event_type: "web_metax_header_connectwallet_click",
                    event_properties: {
                        web_mode_okx: "wallet",
                        site: "okx_web3",
                        wallet_address: walletAddress
                    },
                    event_id: Math.floor(Math.random() * 1000),
                    library: "amplitude-ts/2.11.8",
                    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                },
                {
                    user_id: "",
                    device_id: this.deviceToken,
                    session_id: this.sessionId,
                    time: timestamp + 100,
                    app_version: "1.10.50",
                    platform: "Web",
                    language: "zh_CN",
                    ip: "$remote",
                    insert_id: crypto.randomUUID(),
                    event_type: "onchain_web_wallet_users",
                    event_properties: {
                        web_mode_okx: "wallet",
                        Amount: 1,
                        DeviceId: this.deviceToken,
                        Type: "Connected",
                        site: "okx_web3",
                        wallet_address: walletAddress
                    },
                    event_id: Math.floor(Math.random() * 1000),
                    library: "amplitude-ts/2.11.8",
                    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                }
            ],
            options: {},
            client_upload_time: new Date().toISOString()
        };

        try {
            console.log('📡 发送连接事件...');
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Accept': '*/*',
                    'Content-Type': 'application/json',
                    'Referer': 'https://web3.okx.com/zh-hans/giveaway/jaspervault'
                },
                body: JSON.stringify(eventData)
            });

            const data = await response.json();
            console.log('✅ 连接事件发送成功');
            return data;
        } catch (error) {
            console.error('❌ 发送连接事件失败:', error);
            throw error;
        }
    }

    /**
     * 获取活动详情
     */
    async getGiveawayDetail(walletAddress, navName = 'jaspervault') {
        const timestamp = Date.now();
        const url = `${this.baseUrl}/priapi/v1/dapp/giveaway/getDetailV2?t=${timestamp}`;
        
        const requestBody = {
            navName: navName,
            walletAddress: { "8453": walletAddress }
        };

        const headers = this.generateHeaders(timestamp);

        try {
            console.log('📋 获取活动详情...');
            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(requestBody)
            });

            const data = await response.json();
            console.log('✅ 活动详情获取成功');
            return data;
        } catch (error) {
            console.error('❌ 获取活动详情失败:', error);
            throw error;
        }
    }

    /**
     * 完整的钱包连接流程
     */
    async connectWallet(privateKey, options = {}) {
        const {
            navName = 'jaspervault',
            verifySignature = true
        } = options;

        try {
            console.log('🚀 开始真实钱包连接流程...');
            
            // 1. 创建钱包实例
            const wallet = this.createWallet(privateKey);
            await this.delay(500);

            // 2. 生成并签名连接消息
            const signData = await this.signConnectMessage(wallet);
            await this.delay(500);

            // 3. 验证签名（可选）
            if (verifySignature) {
                const isValid = await this.verifySignature(
                    signData.message, 
                    signData.signature, 
                    signData.address
                );
                if (!isValid) {
                    throw new Error('签名验证失败');
                }
            }
            await this.delay(500);

            // 4. 发送钱包验证
            const verificationResult = await this.sendWalletVerification(signData);
            await this.delay(1000);

            // 5. 发送连接事件
            await this.sendConnectEvent(wallet.address);
            await this.delay(1000);

            // 6. 获取活动详情
            const giveawayDetail = await this.getGiveawayDetail(wallet.address, navName);

            console.log('🎉 钱包连接流程完成！');
            
            return {
                success: true,
                walletAddress: wallet.address,
                deviceToken: this.deviceToken,
                signData: {
                    message: signData.message,
                    signature: signData.signature,
                    timestamp: signData.timestamp
                },
                verificationResult,
                giveawayDetail,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ 钱包连接失败:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 延迟函数
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 批量连接多个钱包
async function batchConnectWallets(wallets, options = {}) {
    const results = [];
    
    console.log(`🚀 开始批量钱包连接 ${wallets.length} 个钱包...\n`);

    for (let i = 0; i < wallets.length; i++) {
        const wallet = wallets[i];
        console.log(`\n📋 处理钱包 ${i + 1}/${wallets.length}: ${wallet.name || 'Unknown'}`);
        
        const connector = new WalletSignerConnect();
        const result = await connector.connectWallet(wallet.privateKey, options);
        
        results.push({
            wallet: wallet.name || wallet.privateKey.substring(0, 10) + '...',
            ...result
        });

        // 钱包之间的延迟
        if (i < wallets.length - 1) {
            console.log('⏸️ 等待3秒后处理下一个钱包...');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    // 生成报告
    const successCount = results.filter(r => r.success).length;
    console.log(`\n📊 批量连接完成: ${successCount}/${wallets.length} 成功`);
    
    return results;
}

// 使用示例
async function main() {
    const wallets = [
        {
            name: '测试钱包1',
            privateKey: 'YOUR_PRIVATE_KEY_1' // 替换为实际私钥
        },
        {
            name: '测试钱包2',
            privateKey: 'YOUR_PRIVATE_KEY_2' // 替换为实际私钥
        }
    ];

    // 检查是否有实际的私钥
    if (wallets.some(w => w.privateKey.includes('YOUR_PRIVATE_KEY'))) {
        console.log('⚠️ 请先配置实际的钱包私钥！');
        console.log('📝 编辑脚本中的 privateKey 字段');
        return;
    }

    try {
        const results = await batchConnectWallets(wallets);
        
        // 保存结果
        const fs = require('fs');
        fs.writeFileSync('wallet_connect_results.json', JSON.stringify(results, null, 2));
        console.log('\n💾 结果已保存到: wallet_connect_results.json');
        
    } catch (error) {
        console.error('❌ 批量连接失败:', error);
    }
}

module.exports = {
    WalletSignerConnect,
    batchConnectWallets
};

if (require.main === module) {
    console.log('🔐 OKX钱包签名连接工具');
    console.log('📝 需要提供钱包私钥进行签名验证\n');
    main().catch(console.error);
}
