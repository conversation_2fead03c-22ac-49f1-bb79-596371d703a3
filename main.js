/**
 * 纯API钱包连接脚本
 * 直接调用OKX的API来模拟钱包连接，无需浏览器
 */

const crypto = require('crypto');

class APIWalletConnect {
    constructor() {
        this.baseUrl = 'https://web3.okx.com';
        this.deviceToken = this.generateDeviceToken();
        this.sessionId = Date.now().toString();
        this.fpToken = null;
        this.fpTokenSignature = null;
    }

    /**
     * 生成设备令牌
     */
    generateDeviceToken() {
        return crypto.randomUUID();
    }

    /**
     * 生成UUID
     */
    generateUUID() {
        return crypto.randomUUID();
    }

    /**
     * 生成基础请求头
     */
    generateHeaders(timestamp, extraHeaders = {}) {
        const headers = {
            'Accept': 'application/json',
            'App-Type': 'web',
            'Content-Type': 'application/json',
            'Device-Token': this.deviceToken,
            'Devid': this.deviceToken,
            'Platform': 'web',
            'Referer': 'https://web3.okx.com/zh-hans/giveaway/jaspervault',
            'X-Cdn': 'https://web3.okx.com',
            'X-Locale': 'zh_CN',
            'X-Request-Timestamp': timestamp.toString(),
            'X-Simulated-Trading': 'undefined',
            'X-Site-Info': '==QfxojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIyUVJiOi42bpdWZyJye',
            'X-Utc': '8',
            'X-Zkdex-Env': '0',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            ...extraHeaders
        };

        return headers;
    }

    /**
     * 生成简单的验证签名 (模拟)
     */
    generateVerifySign(timestamp, body = '') {
        const data = timestamp + body;
        return crypto.createHash('sha256').update(data).digest('hex').substring(0, 32);
    }

    /**
     * 发送连接钱包事件
     */
    async sendConnectWalletEvent(walletAddress) {
        const url = `${this.baseUrl}/amplitude/2/httpapi`;
        const timestamp = Date.now();
        
        const eventData = {
            api_key: "56bf9d43d57f079e506b4f26c70a698f",
            events: [
                {
                    user_id: "",
                    device_id: this.deviceToken,
                    session_id: this.sessionId,
                    time: timestamp,
                    app_version: "1.10.50",
                    platform: "Web",
                    language: "zh_CN",
                    ip: "$remote",
                    insert_id: this.generateUUID(),
                    event_type: "web_metax_header_connectwallet_click",
                    event_properties: {
                        web_mode_okx: "wallet",
                        site: "okx_web3"
                    },
                    event_id: Math.floor(Math.random() * 1000),
                    library: "amplitude-ts/2.11.8",
                    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                },
                {
                    user_id: "",
                    device_id: this.deviceToken,
                    session_id: this.sessionId,
                    time: timestamp + 100,
                    app_version: "1.10.50",
                    platform: "Web",
                    language: "zh_CN",
                    ip: "$remote",
                    insert_id: this.generateUUID(),
                    event_type: "onchain_web_wallet_users",
                    event_properties: {
                        web_mode_okx: "wallet",
                        Amount: 1,
                        DeviceId: this.deviceToken,
                        Type: "Connected",
                        site: "okx_web3"
                    },
                    event_id: Math.floor(Math.random() * 1000),
                    library: "amplitude-ts/2.11.8",
                    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                }
            ],
            options: {},
            client_upload_time: new Date().toISOString()
        };

        try {
            console.log('📡 发送连接钱包事件...');
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Accept': '*/*',
                    'Content-Type': 'application/json',
                    'Referer': 'https://web3.okx.com/zh-hans/giveaway/jaspervault'
                },
                body: JSON.stringify(eventData)
            });

            const data = await response.json();
            console.log('✅ 连接事件发送成功');
            return data;
        } catch (error) {
            console.error('❌ 发送连接事件失败:', error);
            throw error;
        }
    }

    /**
     * 获取活动详情
     */
    async getGiveawayDetail(navName, walletAddress) {
        const timestamp = Date.now();
        const url = `${this.baseUrl}/priapi/v1/dapp/giveaway/getDetailV2?t=${timestamp}`;
        
        const requestBody = {
            navName: navName,
            walletAddress: { "8453": walletAddress }
        };

        const headers = this.generateHeaders(timestamp);

        try {
            console.log('📋 获取活动详情...');
            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(requestBody)
            });

            const data = await response.json();
            console.log('✅ 活动详情获取成功');
            return data;
        } catch (error) {
            console.error('❌ 获取活动详情失败:', error);
            throw error;
        }
    }

    /**
     * 检查任务状态
     */
    async checkTaskStatus(giveawayId, walletAddress, walletAccountId) {
        const timestamp = Date.now();
        const url = `${this.baseUrl}/priapi/v1/dapp/giveaway/task/checkAll?t=${timestamp}`;
        
        const requestBody = {
            giveawayId: giveawayId,
            walletAddress: { "8453": walletAddress },
            walletAccountId: walletAccountId,
            userUniqueId: ""
        };

        const verifySign = this.generateVerifySign(timestamp, JSON.stringify(requestBody));
        const verifyToken = this.generateUUID();

        const headers = this.generateHeaders(timestamp, {
            'Ok-Timestamp': timestamp.toString(),
            'Ok-Verify-Sign': verifySign,
            'Ok-Verify-Token': verifyToken,
            'X-Discover-Auth-Token': 'undefined'
        });

        try {
            console.log('🔍 检查任务状态...');
            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(requestBody)
            });

            const data = await response.json();
            console.log('✅ 任务状态检查完成');
            return data;
        } catch (error) {
            console.error('❌ 检查任务状态失败:', error);
            throw error;
        }
    }

    /**
     * 完整的API连接流程
     */
    async connectWallet(walletAddress, options = {}) {
        const {
            navName = 'jaspervault',
            giveawayId = 389,
            walletAccountId = crypto.randomUUID().toUpperCase()
        } = options;

        try {
            console.log('🚀 开始API钱包连接流程...');
            console.log('📍 钱包地址:', walletAddress);
            console.log('🆔 设备令牌:', this.deviceToken);
            console.log('🔑 账户ID:', walletAccountId);

            // 1. 发送连接事件
            await this.sendConnectWalletEvent(walletAddress);
            await this.delay(1000);

            // 2. 获取活动详情
            const giveawayDetail = await this.getGiveawayDetail(navName, walletAddress);
            await this.delay(1000);

            // 3. 检查任务状态
            const taskStatus = await this.checkTaskStatus(giveawayId, walletAddress, walletAccountId);

            console.log('🎉 API钱包连接流程完成！');
            
            return {
                success: true,
                deviceToken: this.deviceToken,
                walletAddress: walletAddress,
                walletAccountId: walletAccountId,
                giveawayDetail: giveawayDetail,
                taskStatus: taskStatus,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ API连接流程失败:', error);
            return {
                success: false,
                error: error.message,
                deviceToken: this.deviceToken,
                walletAddress: walletAddress,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 延迟函数
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

/**
 * 批量连接多个钱包
 */
async function batchConnectWallets(wallets, options = {}) {
    const results = [];
    
    console.log(`🚀 开始批量API连接 ${wallets.length} 个钱包...\n`);

    for (let i = 0; i < wallets.length; i++) {
        const wallet = wallets[i];
        console.log(`\n📋 处理钱包 ${i + 1}/${wallets.length}: ${wallet.name || wallet.address}`);
        
        const apiConnect = new APIWalletConnect();
        const result = await apiConnect.connectWallet(wallet.address, options);
        
        results.push({
            wallet: wallet.name || wallet.address,
            ...result
        });

        // 钱包之间的延迟
        if (i < wallets.length - 1) {
            console.log('⏸️ 等待3秒后处理下一个钱包...');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    // 生成报告
    const successCount = results.filter(r => r.success).length;
    console.log(`\n📊 批量连接完成: ${successCount}/${wallets.length} 成功`);
    
    results.forEach((result, index) => {
        const status = result.success ? '✅' : '❌';
        console.log(`${index + 1}. ${status} ${result.wallet}`);
        if (!result.success) {
            console.log(`   错误: ${result.error}`);
        }
    });

    return results;
}

// 使用示例
async function main() {
    // 配置钱包列表
    const wallets = [
        {
            name: '钱包1',
            address: '******************************************' // 替换为实际地址
        },
        {
            name: '钱包2', 
            address: '******************************************' // 替换为实际地址
        }
    ];

    // 连接选项
    const options = {
        navName: 'jaspervault',
        giveawayId: 389
    };

    try {
        const results = await batchConnectWallets(wallets, options);
        
        // 保存结果
        const fs = require('fs');
        fs.writeFileSync('api_connect_results.json', JSON.stringify(results, null, 2));
        console.log('\n💾 结果已保存到: api_connect_results.json');
        
    } catch (error) {
        console.error('❌ 批量连接失败:', error);
    }
}

// 单个钱包连接函数
async function connectSingleWallet(walletAddress, options = {}) {
    const apiConnect = new APIWalletConnect();
    return await apiConnect.connectWallet(walletAddress, options);
}

module.exports = {
    APIWalletConnect,
    batchConnectWallets,
    connectSingleWallet
};

// 如果直接运行此文件
if (require.main === module) {
    console.log('🔗 OKX API钱包连接工具');
    console.log('📝 使用方法: 编辑钱包地址后运行\n');
    main().catch(console.error);
}
