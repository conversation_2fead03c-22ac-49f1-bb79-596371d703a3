/**
 * API钱包连接测试脚本
 */

const { APIWalletConnect, connectSingleWallet, batchConnectWallets } = require('./api_wallet_connect');
const config = require('./api_config.json');

/**
 * 测试单个钱包连接
 */
async function testSingleWallet() {
    console.log('🧪 测试单个钱包API连接...\n');
    
    // 使用配置文件中的第一个钱包
    const wallet = config.wallets.find(w => w.enabled);
    if (!wallet) {
        console.error('❌ 请在 api_config.json 中启用至少一个钱包');
        return;
    }

    try {
        const result = await connectSingleWallet(wallet.address, config.settings);
        
        if (result.success) {
            console.log('✅ 单个钱包连接测试成功！');
            console.log('📊 连接结果:', JSON.stringify(result, null, 2));
        } else {
            console.log('❌ 单个钱包连接测试失败');
            console.log('🐛 错误信息:', result.error);
        }
    } catch (error) {
        console.error('❌ 测试执行失败:', error);
    }
}

/**
 * 测试批量钱包连接
 */
async function testBatchWallets() {
    console.log('🧪 测试批量钱包API连接...\n');
    
    const enabledWallets = config.wallets.filter(w => w.enabled);
    if (enabledWallets.length === 0) {
        console.error('❌ 请在 api_config.json 中启用至少一个钱包');
        return;
    }

    try {
        const results = await batchConnectWallets(enabledWallets, config.settings);
        
        console.log('\n📋 批量测试完成！');
        console.log('📊 详细结果已保存到 api_connect_results.json');
        
        return results;
    } catch (error) {
        console.error('❌ 批量测试失败:', error);
    }
}

/**
 * 验证钱包地址格式
 */
function validateWalletAddresses() {
    console.log('🔍 验证钱包地址格式...\n');
    
    const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/;
    let allValid = true;
    
    config.wallets.forEach((wallet, index) => {
        const isValid = ethAddressRegex.test(wallet.address);
        const status = isValid ? '✅' : '❌';
        console.log(`${index + 1}. ${status} ${wallet.name}: ${wallet.address}`);
        
        if (!isValid) {
            allValid = false;
        }
    });
    
    if (allValid) {
        console.log('\n✅ 所有钱包地址格式正确');
    } else {
        console.log('\n❌ 存在无效的钱包地址格式');
    }
    
    return allValid;
}

/**
 * 检查配置文件
 */
function checkConfiguration() {
    console.log('⚙️ 检查配置文件...\n');
    
    // 检查是否有默认地址
    const hasDefaultAddresses = config.wallets.some(w => 
        w.address.includes('YOUR_WALLET_ADDRESS')
    );
    
    if (hasDefaultAddresses) {
        console.log('⚠️ 检测到默认钱包地址，请替换为实际地址：');
        config.wallets.forEach((wallet, index) => {
            if (wallet.address.includes('YOUR_WALLET_ADDRESS')) {
                console.log(`   ${index + 1}. ${wallet.name}: ${wallet.address}`);
            }
        });
        return false;
    }
    
    // 检查启用的钱包数量
    const enabledCount = config.wallets.filter(w => w.enabled).length;
    console.log(`📊 配置统计:`);
    console.log(`   总钱包数: ${config.wallets.length}`);
    console.log(`   启用钱包: ${enabledCount}`);
    console.log(`   活动名称: ${config.settings.navName}`);
    console.log(`   活动ID: ${config.settings.giveawayId}`);
    
    if (enabledCount === 0) {
        console.log('\n⚠️ 没有启用的钱包，请在配置文件中设置 enabled: true');
        return false;
    }
    
    console.log('\n✅ 配置文件检查通过');
    return true;
}

/**
 * 主测试函数
 */
async function runTests() {
    console.log('🚀 OKX API钱包连接测试工具\n');
    
    // 1. 检查配置
    if (!checkConfiguration()) {
        console.log('\n❌ 配置检查失败，请修正配置文件后重试');
        return;
    }
    
    // 2. 验证地址格式
    if (!validateWalletAddresses()) {
        console.log('\n❌ 地址格式验证失败，请检查钱包地址');
        return;
    }
    
    console.log('\n' + '='.repeat(50));
    
    // 3. 测试单个钱包
    await testSingleWallet();
    
    console.log('\n' + '='.repeat(50));
    
    // 4. 测试批量钱包
    const enabledWallets = config.wallets.filter(w => w.enabled);
    if (enabledWallets.length > 1) {
        console.log('\n⏳ 等待5秒后开始批量测试...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        await testBatchWallets();
    } else {
        console.log('\n💡 只有一个启用的钱包，跳过批量测试');
    }
    
    console.log('\n🎉 所有测试完成！');
}

/**
 * 快速连接指定地址
 */
async function quickConnect(walletAddress) {
    console.log(`🚀 快速连接钱包: ${walletAddress}\n`);
    
    const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/;
    if (!ethAddressRegex.test(walletAddress)) {
        console.error('❌ 无效的钱包地址格式');
        return;
    }
    
    try {
        const result = await connectSingleWallet(walletAddress, config.settings);
        
        if (result.success) {
            console.log('✅ 快速连接成功！');
            console.log(`📍 钱包地址: ${result.walletAddress}`);
            console.log(`🆔 设备令牌: ${result.deviceToken}`);
            console.log(`🔑 账户ID: ${result.walletAccountId}`);
        } else {
            console.log('❌ 快速连接失败');
            console.log('🐛 错误信息:', result.error);
        }
        
        return result;
    } catch (error) {
        console.error('❌ 快速连接执行失败:', error);
    }
}

// 导出函数
module.exports = {
    testSingleWallet,
    testBatchWallets,
    validateWalletAddresses,
    checkConfiguration,
    runTests,
    quickConnect
};

// 命令行参数处理
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length > 0) {
        // 如果提供了钱包地址参数，执行快速连接
        const walletAddress = args[0];
        quickConnect(walletAddress).catch(console.error);
    } else {
        // 否则运行完整测试
        runTests().catch(console.error);
    }
}
