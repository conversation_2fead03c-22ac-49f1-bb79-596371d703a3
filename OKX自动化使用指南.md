# OKX Jasper Vault 任务自动化使用指南

## 🎯 概述

基于真实API捕获，实现OKX Jasper Vault活动的完全自动化，包括：
- Twitter账号绑定
- 4个任务自动完成
- 最终验证

## 📋 捕获到的关键API

### 1. **Twitter绑定相关**
```
POST /priapi/v1/dapp/oauth2/oauth2-url
POST /priapi/v1/dapp/oauth2/call-back
```

### 2. **任务操作相关**
```
POST /priapi/v1/dapp/giveaway/clickTask
POST /priapi/v1/dapp/giveaway/task/check
```

### 3. **验证相关**
```
POST /priapi/v1/dapp/giveaway/verify
```

## 🚀 使用步骤

### 第一步：准备Twitter Token
确保你已经有 `twitter_tokens_1754344648786.json` 文件，包含：
- `ct0`: CSRF Token
- `twid`: 用户ID
- `userInfo`: 用户信息

### 第二步：在OKX页面执行
1. 打开 https://web3.okx.com/zh-hans/giveaway/jaspervault
2. 确保钱包已连接
3. 在控制台粘贴并运行 `okx_jasper_vault_complete_automation.js`

### 第三步：启动自动化
```javascript
// 启动完整自动化
await startOKXJasperVaultAutomation("0x你的钱包地址");
```

## 📊 执行流程

### 1. **Twitter绑定流程**
```
获取OAuth URL → 模拟OAuth回调 → 绑定成功
```

### 2. **任务执行流程**
```
点击任务 → 等待2秒 → 检查状态 → 下一个任务
```

### 3. **任务列表**
1. 关注 @Jaspervault 的 X
2. 关注 @wallet 的 X  
3. 加入 Jasper Vault 官方 Discord 社区
4. OKX Wallet 持有至少 10 USDT 等值代币

### 4. **最终验证**
所有任务完成后自动执行验证

## 🔧 技术细节

### **请求头生成**
脚本自动生成所需的请求头：
- `Ok-Verify-Sign`: 验证签名
- `Ok-Verify-Token`: 验证令牌
- `X-FpToken`: 设备指纹令牌
- `X-FpToken-Signature`: 指纹签名

### **任务配置**
```javascript
const tasks = [
    {
        id: 1,
        name: '关注 @Jaspervault 的 X',
        type: 'twitter',
        platform: 1,
        targetAccount: 'jaspervault'
    },
    // ... 其他任务
];
```

### **API调用示例**
```javascript
// 点击任务
const payload = {
    giveawayId: 'jaspervault',
    taskId: 1,
    walletAddress: '0x...',
    chainId: 8453,
    platform: 1,
    bizType: 1
};

const response = await fetch('/priapi/v1/dapp/giveaway/clickTask', {
    method: 'POST',
    headers: generateHeaders(),
    body: JSON.stringify(payload)
});
```

## 📈 成功率优化

### **1. 延迟设置**
- 任务间延迟：3秒
- 检查状态延迟：2秒
- 避免请求过快被检测

### **2. 错误处理**
- 每个API调用都有错误捕获
- 失败任务不影响后续执行
- 详细的日志输出

### **3. 状态验证**
- 每个任务执行后检查状态
- 确保任务真正完成
- 最终统一验证

## 🔍 调试和监控

### **控制台输出**
```
🚀 启动OKX Jasper Vault自动化...
✅ Twitter认证信息验证通过
🐦 开始Twitter账号绑定...
📡 获取OAuth URL...
🔄 模拟OAuth回调...
🎯 开始执行所有任务...
🔄 执行任务 1: 关注 @Jaspervault 的 X
👆 点击任务: 关注 @Jaspervault 的 X
📡 点击任务响应: {code: 0, data: {...}}
🔍 检查任务状态: 关注 @Jaspervault 的 X
📡 任务状态响应: {code: 0, data: {...}}
✅ 任务 1 成功
...
🎉 任务执行完成，成功: 4/4
✅ 执行最终验证...
🎉 自动化执行成功！
```

### **返回结果**
```javascript
{
    success: true,
    twitterBinding: { success: true, data: {...} },
    taskResults: [
        { task: {...}, success: true, clickResult: {...}, checkResult: {...} },
        // ... 其他任务结果
    ],
    verification: { success: true, data: {...} }
}
```

## ⚠️ 注意事项

### **1. 环境要求**
- 必须在OKX页面执行
- 钱包必须已连接
- Twitter Token必须有效

### **2. 签名算法**
当前使用示例签名，如果失败可能需要：
- 分析真实的签名算法
- 更新 `generateVerifySign()` 方法
- 获取真实的设备指纹

### **3. 频率限制**
- 不要过于频繁执行
- 建议每个账号间隔5-10分钟
- 避免同时处理大量账号

### **4. 错误处理**
如果遇到错误：
1. 检查控制台错误信息
2. 确认钱包连接状态
3. 验证Twitter Token有效性
4. 检查网络连接

## 🔄 批量处理

### **多账号处理示例**
```javascript
const accounts = [
    {
        walletAddress: "0x1234...",
        twitterTokens: { /* tokens1 */ }
    },
    {
        walletAddress: "0x5678...", 
        twitterTokens: { /* tokens2 */ }
    }
];

for (const account of accounts) {
    console.log(`🔄 处理账号: ${account.walletAddress}`);
    
    // 更新Twitter tokens
    const automation = new OKXJasperVaultAutomation(
        account.twitterTokens, 
        account.walletAddress
    );
    
    const result = await automation.execute();
    
    if (result.success) {
        console.log(`✅ ${account.walletAddress} 处理成功`);
    } else {
        console.log(`❌ ${account.walletAddress} 处理失败:`, result.error);
    }
    
    // 账号间延迟
    await new Promise(resolve => setTimeout(resolve, 300000)); // 5分钟
}
```

## 📞 故障排除

### **常见问题**

1. **Twitter绑定失败**
   - 检查Token是否过期
   - 确认用户ID和CSRF Token正确

2. **任务点击失败**
   - 检查钱包连接状态
   - 确认giveawayId正确

3. **验证失败**
   - 确保所有任务都已完成
   - 检查网络连接

4. **签名错误**
   - 可能需要更新签名算法
   - 检查设备指纹生成

### **调试技巧**
- 开启详细日志输出
- 检查网络请求和响应
- 对比手动操作的API调用
- 使用浏览器开发者工具监控

---

**🎉 祝你使用愉快！如有问题，请检查控制台输出和错误信息。**
