/**
 * 纯无头浏览器钱包连接自动化脚本
 * 专为生产环境设计，只支持无头模式
 */

const puppeteer = require('puppeteer');

class HeadlessWalletAutomation {
    constructor(options = {}) {
        this.options = {
            slowMo: options.slowMo || 50,
            timeout: options.timeout || 30000,
            retries: options.retries || 3,
            ...options
        };
        this.browser = null;
        this.page = null;
    }

    /**
     * 初始化无头浏览器
     */
    async init() {
        console.log('🚀 启动无头浏览器...');
        
        this.browser = await puppeteer.launch({
            headless: 'new',
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--no-first-run',
                '--no-zygote'
            ]
        });

        this.page = await this.browser.newPage();
        await this.page.setViewport({ width: 1280, height: 720 });
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        
        console.log('✅ 无头浏览器初始化完成');
    }

    /**
     * 注入钱包模拟器
     */
    async injectWalletSimulator(walletAddress) {
        console.log('🔧 注入钱包模拟器...');
        
        await this.page.evaluateOnNewDocument((address) => {
            window.okxwallet = {
                isOkxWallet: true,
                isConnected: false,
                selectedAddress: null,
                chainId: '0x2105', // Base链
                
                async connect() {
                    this.isConnected = true;
                    this.selectedAddress = address;
                    window.dispatchEvent(new CustomEvent('okxwallet_accountsChanged', {
                        detail: [address]
                    }));
                    return [address];
                },
                
                async request(args) {
                    switch (args.method) {
                        case 'eth_requestAccounts':
                            return await this.connect();
                        case 'eth_accounts':
                            return this.isConnected ? [this.selectedAddress] : [];
                        case 'eth_chainId':
                            return this.chainId;
                        case 'wallet_switchEthereumChain':
                            this.chainId = args.params[0].chainId;
                            return null;
                        case 'personal_sign':
                        case 'eth_sign':
                            return '0x' + '0'.repeat(130);
                        default:
                            return null;
                    }
                },
                
                on(event, callback) {
                    window.addEventListener(`okxwallet_${event}`, callback);
                },
                
                removeListener(event, callback) {
                    window.removeEventListener(`okxwallet_${event}`, callback);
                }
            };
            
            window.ethereum = window.okxwallet;
            console.log('✅ 钱包模拟器已注入');
        }, walletAddress);
    }

    /**
     * 导航到目标页面
     */
    async navigateToPage(url) {
        console.log('🌐 导航到页面:', url);
        await this.page.goto(url, {
            waitUntil: 'networkidle2',
            timeout: this.options.timeout
        });
        console.log('✅ 页面加载完成');
    }

    /**
     * 查找并点击连接钱包按钮
     */
    async clickConnectButton() {
        console.log('🔍 查找连接钱包按钮...');
        
        await this.page.waitForTimeout(2000);
        
        // 尝试多种方式查找按钮
        const buttonFound = await this.page.evaluate(() => {
            // 方法1: 通过文本内容查找
            const buttons = Array.from(document.querySelectorAll('button, div[role="button"], a'));
            const connectButton = buttons.find(btn => {
                const text = btn.textContent || btn.innerText || '';
                return text.includes('连接') && (text.includes('钱包') || text.includes('OKX'));
            });
            
            if (connectButton) {
                connectButton.click();
                return true;
            }
            
            // 方法2: 通过常见的CSS类名查找
            const selectors = [
                '[class*="connect"]',
                '[class*="wallet"]',
                '[data-testid*="connect"]'
            ];
            
            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element && element.textContent.includes('连接')) {
                    element.click();
                    return true;
                }
            }
            
            return false;
        });
        
        if (buttonFound) {
            console.log('✅ 连接钱包按钮已点击');
        } else {
            throw new Error('未找到连接钱包按钮');
        }
        
        await this.page.waitForTimeout(1000);
    }

    /**
     * 处理钱包选择
     */
    async selectOKXWallet() {
        console.log('🔍 选择OKX钱包...');
        
        await this.page.waitForTimeout(2000);
        
        const walletSelected = await this.page.evaluate(() => {
            const elements = Array.from(document.querySelectorAll('*'));
            const okxElement = elements.find(el => {
                const text = el.textContent || el.innerText || '';
                return text.includes('OKX') && (text.includes('Wallet') || text.includes('钱包'));
            });
            
            if (okxElement) {
                okxElement.click();
                return true;
            }
            return false;
        });
        
        if (walletSelected) {
            console.log('✅ OKX钱包已选择');
        } else {
            console.log('⚠️ 未找到OKX钱包选项，可能已自动连接');
        }
        
        await this.page.waitForTimeout(1000);
    }

    /**
     * 等待连接完成
     */
    async waitForConnection() {
        console.log('⏳ 等待钱包连接完成...');
        
        try {
            await this.page.waitForFunction(() => {
                return window.okxwallet && window.okxwallet.isConnected;
            }, { timeout: 10000 });
            
            const connectionInfo = await this.page.evaluate(() => {
                return {
                    isConnected: window.okxwallet?.isConnected,
                    address: window.okxwallet?.selectedAddress,
                    chainId: window.okxwallet?.chainId
                };
            });
            
            console.log('✅ 钱包连接成功！');
            console.log('📊 连接信息:', connectionInfo);
            return connectionInfo;
            
        } catch (error) {
            throw new Error('钱包连接超时');
        }
    }

    /**
     * 完整的连接流程
     */
    async connectWallet(privateKey, walletAddress, targetUrl) {
        let retries = this.options.retries;
        
        while (retries > 0) {
            try {
                console.log(`🚀 开始钱包连接 (剩余重试: ${retries})`);
                
                await this.init();
                await this.injectWalletSimulator(walletAddress);
                await this.navigateToPage(targetUrl);
                await this.clickConnectButton();
                await this.selectOKXWallet();
                const result = await this.waitForConnection();
                
                console.log('🎉 钱包连接成功完成！');
                return result;
                
            } catch (error) {
                console.error(`❌ 连接失败: ${error.message}`);
                retries--;
                
                if (retries > 0) {
                    console.log(`🔄 ${3 - retries + 1}秒后重试...`);
                    await this.close();
                    await new Promise(resolve => setTimeout(resolve, 3000));
                } else {
                    throw error;
                }
            }
        }
    }

    /**
     * 截图
     */
    async screenshot(filename = `wallet_connect_${Date.now()}.png`) {
        if (this.page) {
            await this.page.screenshot({ 
                path: filename, 
                fullPage: true 
            });
            console.log(`📸 截图已保存: ${filename}`);
        }
    }

    /**
     * 关闭浏览器
     */
    async close() {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
            this.page = null;
        }
    }
}

// 批量连接多个钱包
async function batchConnectWallets(wallets, targetUrl) {
    const results = [];
    
    for (let i = 0; i < wallets.length; i++) {
        const wallet = wallets[i];
        console.log(`\n📋 处理钱包 ${i + 1}/${wallets.length}: ${wallet.name}`);
        
        const automation = new HeadlessWalletAutomation();
        
        try {
            const result = await automation.connectWallet(
                wallet.privateKey,
                wallet.address,
                targetUrl
            );
            
            results.push({
                wallet: wallet.name,
                success: true,
                result: result
            });
            
            await automation.screenshot(`${wallet.name}_success.png`);
            
        } catch (error) {
            console.error(`❌ 钱包 ${wallet.name} 连接失败:`, error.message);
            results.push({
                wallet: wallet.name,
                success: false,
                error: error.message
            });
            
            await automation.screenshot(`${wallet.name}_error.png`);
        } finally {
            await automation.close();
        }
        
        // 钱包之间的延迟
        if (i < wallets.length - 1) {
            console.log('⏸️ 等待3秒后处理下一个钱包...');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }
    
    // 输出总结
    const successCount = results.filter(r => r.success).length;
    console.log(`\n📊 批量连接完成: ${successCount}/${wallets.length} 成功`);
    
    return results;
}

// 使用示例
async function main() {
    const wallets = [
        {
            name: '钱包1',
            privateKey: 'YOUR_PRIVATE_KEY_1',
            address: 'YOUR_WALLET_ADDRESS_1'
        },
        {
            name: '钱包2',
            privateKey: 'YOUR_PRIVATE_KEY_2', 
            address: 'YOUR_WALLET_ADDRESS_2'
        }
    ];
    
    const targetUrl = 'https://web3.okx.com/zh-hans/giveaway/jaspervault';
    
    try {
        await batchConnectWallets(wallets, targetUrl);
    } catch (error) {
        console.error('批量连接失败:', error);
    }
}

module.exports = { HeadlessWalletAutomation, batchConnectWallets };

if (require.main === module) {
    main().catch(console.error);
}
