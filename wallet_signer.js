/**
 * 钱包签名器 - 支持完整钱包功能的连接脚本
 * 包含签名、交易、消息签名等功能
 */

class WalletSigner {
    constructor(privateKey = null) {
        this.privateKey = privateKey || this.generatePrivateKey();
        this.address = this.deriveAddress(this.privateKey);
        this.chainId = 8453; // Base链
        this.connected = false;
        
        console.log('🔐 钱包签名器初始化');
        console.log('💰 钱包地址:', this.address);
        console.log('🔑 私钥:', this.privateKey.substring(0, 10) + '...');
    }
    
    /**
     * 生成私钥
     */
    generatePrivateKey() {
        const chars = '0123456789abcdef';
        let privateKey = '0x';
        for (let i = 0; i < 64; i++) {
            privateKey += chars[Math.floor(Math.random() * chars.length)];
        }
        return privateKey;
    }
    
    /**
     * 从私钥推导地址（简化版）
     */
    deriveAddress(privateKey) {
        // 简化的地址生成（实际应该使用椭圆曲线加密）
        const hash = this.simpleHash(privateKey);
        return '0x' + hash.substring(0, 40);
    }
    
    /**
     * 简单哈希函数
     */
    simpleHash(input) {
        let hash = 0;
        for (let i = 0; i < input.length; i++) {
            const char = input.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(16).padStart(40, '0');
    }
    
    /**
     * 连接钱包到网页
     */
    async connect() {
        console.log('🔗 开始连接钱包到网页...');
        
        try {
            // 1. 注入钱包对象
            this.injectWalletProvider();
            
            // 2. 设置连接状态
            this.setConnectionState();
            
            // 3. 触发连接事件
            this.triggerConnectionEvents();
            
            this.connected = true;
            console.log('✅ 钱包连接成功！');
            
            return {
                success: true,
                address: this.address,
                chainId: this.chainId
            };
            
        } catch (error) {
            console.error('❌ 钱包连接失败:', error);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 注入钱包提供者
     */
    injectWalletProvider() {
        console.log('🔧 注入钱包提供者...');
        
        const self = this;
        
        // 创建钱包提供者对象
        const walletProvider = {
            isOKXWallet: true,
            isMetaMask: false,
            isConnected: () => self.connected,
            selectedAddress: self.address,
            chainId: `0x${self.chainId.toString(16)}`,
            networkVersion: self.chainId.toString(),
            
            // 账户相关方法
            request: async (params) => {
                console.log('📡 钱包请求:', params.method, params.params);
                return await self.handleRequest(params);
            },
            
            // 发送方法（兼容性）
            send: async (method, params) => {
                return await self.handleRequest({ method, params });
            },
            
            sendAsync: (payload, callback) => {
                self.handleRequest(payload).then(result => {
                    callback(null, { id: payload.id, jsonrpc: '2.0', result });
                }).catch(error => {
                    callback(error, null);
                });
            },
            
            // 事件监听
            on: (event, handler) => {
                console.log('🔧 监听事件:', event);
                self.addEventListener(event, handler);
            },
            
            removeListener: (event, handler) => {
                console.log('🔧 移除监听:', event);
                self.removeEventListener(event, handler);
            },
            
            // 签名方法
            signMessage: async (message) => {
                return await self.signMessage(message);
            },
            
            signTransaction: async (transaction) => {
                return await self.signTransaction(transaction);
            },
            
            // 个人签名
            personal_sign: async (message, address) => {
                return await self.personalSign(message, address);
            }
        };
        
        // 设置到全局对象
        window.ethereum = walletProvider;
        window.okxwallet = walletProvider;
        
        console.log('✅ 钱包提供者注入完成');
    }
    
    /**
     * 处理钱包请求
     */
    async handleRequest(params) {
        const { method, params: requestParams } = params;
        
        console.log(`🔧 处理请求: ${method}`);
        
        switch (method) {
            case 'eth_requestAccounts':
                return [this.address];
                
            case 'eth_accounts':
                return this.connected ? [this.address] : [];
                
            case 'eth_chainId':
                return `0x${this.chainId.toString(16)}`;
                
            case 'net_version':
                return this.chainId.toString();
                
            case 'eth_getBalance':
                return '0x1bc16d674ec80000'; // 2 ETH
                
            case 'eth_gasPrice':
                return '0x9184e72a000'; // 10 Gwei
                
            case 'eth_estimateGas':
                return '0x5208'; // 21000 gas
                
            case 'eth_getTransactionCount':
                return '0x1'; // nonce = 1
                
            case 'eth_sendTransaction':
                return await this.sendTransaction(requestParams[0]);
                
            case 'eth_signTransaction':
                return await this.signTransaction(requestParams[0]);
                
            case 'eth_sign':
                return await this.ethSign(requestParams[1], requestParams[0]);
                
            case 'personal_sign':
                return await this.personalSign(requestParams[0], requestParams[1]);
                
            case 'eth_signTypedData':
            case 'eth_signTypedData_v3':
            case 'eth_signTypedData_v4':
                return await this.signTypedData(requestParams[1], requestParams[0]);
                
            case 'wallet_switchEthereumChain':
                return await this.switchChain(requestParams[0]);
                
            case 'wallet_addEthereumChain':
                return await this.addChain(requestParams[0]);
                
            default:
                console.log(`⚠️ 未支持的方法: ${method}`);
                throw new Error(`Unsupported method: ${method}`);
        }
    }
    
    /**
     * 发送交易
     */
    async sendTransaction(transaction) {
        console.log('📤 发送交易:', transaction);
        
        // 签名交易
        const signedTx = await this.signTransaction(transaction);
        
        // 生成交易哈希
        const txHash = '0x' + this.simpleHash(JSON.stringify(transaction) + Date.now());
        
        console.log('✅ 交易已发送，哈希:', txHash);
        return txHash;
    }
    
    /**
     * 签名交易
     */
    async signTransaction(transaction) {
        console.log('✍️ 签名交易:', transaction);
        
        const txData = {
            nonce: transaction.nonce || '0x1',
            gasPrice: transaction.gasPrice || '0x9184e72a000',
            gasLimit: transaction.gas || transaction.gasLimit || '0x5208',
            to: transaction.to,
            value: transaction.value || '0x0',
            data: transaction.data || '0x',
            chainId: this.chainId
        };
        
        // 生成签名（简化版）
        const signature = this.generateSignature(JSON.stringify(txData));
        
        const signedTx = {
            ...txData,
            v: '0x1c',
            r: signature.r,
            s: signature.s,
            rawTransaction: '0x' + this.simpleHash(JSON.stringify(txData))
        };
        
        console.log('✅ 交易签名完成');
        return signedTx;
    }
    
    /**
     * 个人签名
     */
    async personalSign(message, address) {
        console.log('✍️ 个人签名:', message, address);
        
        if (address.toLowerCase() !== this.address.toLowerCase()) {
            throw new Error('Address mismatch');
        }
        
        const signature = this.generateSignature(message);
        const fullSignature = signature.r + signature.s.substring(2) + '1c';
        
        console.log('✅ 个人签名完成');
        return '0x' + fullSignature;
    }
    
    /**
     * ETH签名
     */
    async ethSign(message, address) {
        console.log('✍️ ETH签名:', message, address);
        return await this.personalSign(message, address);
    }
    
    /**
     * 类型化数据签名
     */
    async signTypedData(address, typedData) {
        console.log('✍️ 类型化数据签名:', address, typedData);
        
        if (address.toLowerCase() !== this.address.toLowerCase()) {
            throw new Error('Address mismatch');
        }
        
        const dataString = typeof typedData === 'string' ? typedData : JSON.stringify(typedData);
        const signature = this.generateSignature(dataString);
        const fullSignature = signature.r + signature.s.substring(2) + '1c';
        
        console.log('✅ 类型化数据签名完成');
        return '0x' + fullSignature;
    }
    
    /**
     * 生成签名
     */
    generateSignature(data) {
        const hash = this.simpleHash(this.privateKey + data);
        
        return {
            r: '0x' + hash.substring(0, 32).padStart(64, '0'),
            s: '0x' + hash.substring(32, 64).padStart(64, '0')
        };
    }
    
    /**
     * 切换链
     */
    async switchChain(chainParams) {
        const newChainId = parseInt(chainParams.chainId, 16);
        console.log('🔄 切换链:', newChainId);
        
        this.chainId = newChainId;
        window.ethereum.chainId = `0x${newChainId.toString(16)}`;
        
        // 触发链切换事件
        this.triggerEvent('chainChanged', `0x${newChainId.toString(16)}`);
        
        return null;
    }
    
    /**
     * 添加链
     */
    async addChain(chainParams) {
        console.log('➕ 添加链:', chainParams);
        return null; // 假设总是成功
    }
    
    /**
     * 设置连接状态
     */
    setConnectionState() {
        console.log('💾 设置连接状态...');
        
        localStorage.setItem('walletConnected', 'true');
        localStorage.setItem('walletAddress', this.address);
        localStorage.setItem('walletPrivateKey', this.privateKey);
        localStorage.setItem('chainId', this.chainId.toString());
        localStorage.setItem('walletType', 'OKX');
        
        sessionStorage.setItem('walletConnected', 'true');
        sessionStorage.setItem('walletAddress', this.address);
    }
    
    /**
     * 触发连接事件
     */
    triggerConnectionEvents() {
        console.log('📡 触发连接事件...');
        
        // 触发账户连接事件
        this.triggerEvent('accountsChanged', [this.address]);
        
        // 触发连接事件
        this.triggerEvent('connect', { chainId: `0x${this.chainId.toString(16)}` });
        
        // 触发自定义钱包连接事件
        window.dispatchEvent(new CustomEvent('walletConnected', {
            detail: {
                address: this.address,
                chainId: this.chainId,
                provider: 'OKX'
            }
        }));
    }
    
    /**
     * 触发事件
     */
    triggerEvent(eventName, data) {
        if (this.eventListeners && this.eventListeners[eventName]) {
            this.eventListeners[eventName].forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error('事件处理器错误:', error);
                }
            });
        }
    }
    
    /**
     * 添加事件监听器
     */
    addEventListener(event, handler) {
        if (!this.eventListeners) {
            this.eventListeners = {};
        }
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(handler);
    }
    
    /**
     * 移除事件监听器
     */
    removeEventListener(event, handler) {
        if (this.eventListeners && this.eventListeners[event]) {
            const index = this.eventListeners[event].indexOf(handler);
            if (index > -1) {
                this.eventListeners[event].splice(index, 1);
            }
        }
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        console.log('🔌 断开钱包连接...');
        
        this.connected = false;
        
        // 清理存储
        localStorage.removeItem('walletConnected');
        localStorage.removeItem('walletAddress');
        localStorage.removeItem('walletPrivateKey');
        localStorage.removeItem('chainId');
        localStorage.removeItem('walletType');
        
        sessionStorage.removeItem('walletConnected');
        sessionStorage.removeItem('walletAddress');
        
        // 清理全局对象
        delete window.ethereum;
        delete window.okxwallet;
        
        // 触发断开事件
        this.triggerEvent('disconnect', { code: 4900, message: 'User disconnected' });
        
        console.log('✅ 钱包连接已断开');
    }
    
    /**
     * 获取钱包状态
     */
    getStatus() {
        return {
            connected: this.connected,
            address: this.address,
            privateKey: this.privateKey,
            chainId: this.chainId,
            canSign: true
        };
    }
}

// 全局函数
window.connectSignerWallet = async function(privateKey = null) {
    console.log('🚀 启动签名钱包连接...');
    
    const signer = new WalletSigner(privateKey);
    const result = await signer.connect();
    
    if (result.success) {
        console.log('🎉 签名钱包连接成功！');
        console.log('📍 钱包地址:', result.address);
        console.log('🔐 支持签名功能');
        
        // 保存签名器实例
        window.walletSigner = signer;
        
        // 测试签名功能
        console.log('\n🧪 测试签名功能:');
        try {
            const testMessage = 'Hello, OKX!';
            const signature = await signer.personalSign(testMessage, signer.address);
            console.log('✅ 测试签名成功:', signature.substring(0, 20) + '...');
        } catch (error) {
            console.log('❌ 测试签名失败:', error);
        }
        
    } else {
        console.log('❌ 签名钱包连接失败:', result.error);
    }
    
    return result;
};

window.disconnectSignerWallet = function() {
    if (window.walletSigner) {
        window.walletSigner.disconnect();
        delete window.walletSigner;
    }
};

window.getSignerStatus = function() {
    if (window.walletSigner) {
        return window.walletSigner.getStatus();
    }
    return { connected: false, canSign: false };
};

// 签名测试函数
window.testSigning = async function(message = 'Test message') {
    if (!window.walletSigner) {
        console.log('❌ 钱包未连接');
        return;
    }
    
    try {
        console.log('🧪 测试签名:', message);
        const signature = await window.walletSigner.personalSign(message, window.walletSigner.address);
        console.log('✅ 签名结果:', signature);
        return signature;
    } catch (error) {
        console.log('❌ 签名失败:', error);
        return null;
    }
};

console.log('🔐 钱包签名器已加载');
console.log('📖 使用方法:');
console.log('  await connectSignerWallet(); // 自动生成私钥');
console.log('  await connectSignerWallet("0x私钥"); // 使用指定私钥');
console.log('  await testSigning("消息内容"); // 测试签名');
console.log('  disconnectSignerWallet(); // 断开连接');
console.log('  getSignerStatus(); // 获取状态');
