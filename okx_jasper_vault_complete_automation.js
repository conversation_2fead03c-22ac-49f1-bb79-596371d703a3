/**
 * OKX Jasper Vault 完整任务自动化脚本
 * 基于捕获的真实API实现
 */

class OKXJasperVaultAutomation {
    constructor(twitterTokens, walletAddress) {
        // Twitter认证信息
        this.twitterAuth = {
            csrfToken: twitterTokens.cookies?.ct0 || null,
            userId: this.extractUserId(twitterTokens.cookies?.twid),
            screenName: this.extractScreenName(twitterTokens.userInfo) || 'dsqqzx'
        };
        
        // OKX配置
        this.walletAddress = walletAddress;
        this.giveawayId = 'jaspervault';
        this.chainId = 8453; // Base链
        this.baseUrl = 'https://web3.okx.com/priapi/v1/dapp';
        
        // 任务配置
        this.tasks = [
            {
                id: 1,
                name: '关注 @Jaspervault 的 X',
                type: 'twitter',
                platform: 1,
                targetAccount: 'jaspervault'
            },
            {
                id: 2,
                name: '关注 @wallet 的 X', 
                type: 'twitter',
                platform: 1,
                targetAccount: 'wallet'
            },
            {
                id: 3,
                name: '加入 Jasper Vault 官方 Discord 社区',
                type: 'discord',
                platform: 2
            },
            {
                id: 4,
                name: 'OKX Wallet 持有至少 10 USDT 等值代币',
                type: 'wallet_balance',
                platform: 0
            }
        ];
        
        console.log('🚀 OKX Jasper Vault 自动化初始化完成');
        console.log('🐦 Twitter用户:', this.twitterAuth.screenName);
        console.log('💰 钱包地址:', this.walletAddress);
    }
    
    /**
     * 从twid提取用户ID
     */
    extractUserId(twid) {
        if (!twid) return null;
        const match = twid.match(/u%3D(\d+)/);
        return match ? match[1] : null;
    }
    
    /**
     * 从用户信息提取用户名
     */
    extractScreenName(userInfo) {
        if (!userInfo) return null;
        
        for (const key in userInfo) {
            const value = userInfo[key];
            if (typeof value === 'string') {
                const match = value.match(/@([a-zA-Z0-9_]+)/);
                if (match) return match[1];
                
                const followMatch = value.match(/Follow @([a-zA-Z0-9_]+)/);
                if (followMatch) return followMatch[1];
            }
        }
        return null;
    }
    
    /**
     * 生成OKX请求头
     */
    generateHeaders(timestamp = Date.now()) {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Ok-Timestamp': timestamp.toString(),
            'Ok-Verify-Sign': this.generateVerifySign(timestamp),
            'Ok-Verify-Token': this.generateVerifyToken(),
            'X-FpToken': this.generateFpToken(),
            'X-FpToken-Signature': this.generateFpTokenSignature(),
            'X-Id-Group': this.generateIdGroup(),
            'X-Request-Timestamp': timestamp.toString(),
            'Device-Token': this.generateDeviceToken(),
            'X-Locale': 'zh_CN',
            'X-Utc': '8',
            'User-Agent': navigator.userAgent,
            'Referer': `https://web3.okx.com/zh-hans/giveaway/${this.giveawayId}`
        };
    }
    
    /**
     * 主要执行函数
     */
    async execute() {
        console.log('🎯 开始执行OKX Jasper Vault任务自动化...');
        
        try {
            // 1. 验证Twitter认证信息
            if (!this.validateTwitterAuth()) {
                throw new Error('Twitter认证信息不完整');
            }
            
            // 2. 执行Twitter绑定
            const bindResult = await this.bindTwitterAccount();
            if (!bindResult.success) {
                console.log('⚠️ Twitter绑定失败，继续执行任务...');
            }
            
            // 3. 执行所有任务
            const taskResults = await this.executeAllTasks();
            
            // 4. 最终验证
            const verifyResult = await this.verifyCompletion();
            
            console.log('🎉 所有任务执行完成！');
            return {
                success: true,
                twitterBinding: bindResult,
                taskResults: taskResults,
                verification: verifyResult
            };
            
        } catch (error) {
            console.error('❌ 执行失败:', error);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 验证Twitter认证信息
     */
    validateTwitterAuth() {
        if (!this.twitterAuth.csrfToken || !this.twitterAuth.userId) {
            console.error('❌ 缺少Twitter认证信息');
            return false;
        }
        
        console.log('✅ Twitter认证信息验证通过');
        return true;
    }
    
    /**
     * 绑定Twitter账号
     */
    async bindTwitterAccount() {
        console.log('🐦 开始Twitter账号绑定...');
        
        try {
            // 1. 获取OAuth URL
            const oauthUrlResult = await this.getOAuthUrl();
            if (!oauthUrlResult.success) {
                return { success: false, error: 'Failed to get OAuth URL' };
            }
            
            // 2. 模拟OAuth回调
            const callbackResult = await this.simulateOAuthCallback(oauthUrlResult.data);
            
            return callbackResult;
            
        } catch (error) {
            console.error('❌ Twitter绑定失败:', error);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 获取OAuth URL
     */
    async getOAuthUrl() {
        console.log('📡 获取OAuth URL...');
        
        const timestamp = Date.now();
        const payload = {
            domain: 'web3.okx.com',
            walletAddress: this.walletAddress,
            platform: 1, // Twitter
            bizType: 1,
            bizRequestData: {
                giveawayId: this.giveawayId,
                chainId: this.chainId
            }
        };
        
        try {
            const response = await fetch(`${this.baseUrl}/oauth2/oauth2-url?t=${timestamp}`, {
                method: 'POST',
                headers: this.generateHeaders(timestamp),
                body: JSON.stringify(payload)
            });
            
            const result = await response.json();
            console.log('📡 OAuth URL响应:', result);
            
            return {
                success: result.code === 0,
                data: result.data
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 模拟OAuth回调
     */
    async simulateOAuthCallback(oauthData) {
        console.log('🔄 模拟OAuth回调...');
        
        const timestamp = Date.now();
        
        // 生成模拟的授权码
        const authCode = this.generateAuthCode();
        
        const payload = {
            code: authCode,
            state: oauthData?.state || 'state',
            walletAddress: this.walletAddress,
            platform: 1,
            bizType: 1,
            bizRequestData: {
                giveawayId: this.giveawayId,
                chainId: this.chainId
            },
            userInfo: {
                userId: this.twitterAuth.userId,
                screenName: this.twitterAuth.screenName,
                csrfToken: this.twitterAuth.csrfToken
            }
        };
        
        try {
            const response = await fetch(`${this.baseUrl}/oauth2/call-back?t=${timestamp}`, {
                method: 'POST',
                headers: this.generateHeaders(timestamp),
                body: JSON.stringify(payload)
            });
            
            const result = await response.json();
            console.log('📡 OAuth回调响应:', result);
            
            return {
                success: result.code === 0,
                data: result
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 执行所有任务
     */
    async executeAllTasks() {
        console.log('🎯 开始执行所有任务...');
        
        const results = [];
        
        for (let i = 0; i < this.tasks.length; i++) {
            const task = this.tasks[i];
            console.log(`🔄 执行任务 ${i + 1}: ${task.name}`);
            
            try {
                // 1. 点击任务
                const clickResult = await this.clickTask(task);
                
                // 2. 检查任务状态
                await this.delay(2000);
                const checkResult = await this.checkTaskStatus(task);
                
                results.push({
                    task: task,
                    clickResult: clickResult,
                    checkResult: checkResult,
                    success: clickResult.success && checkResult.success
                });
                
                console.log(`${results[i].success ? '✅' : '❌'} 任务 ${i + 1} ${results[i].success ? '成功' : '失败'}`);
                
                // 任务间延迟
                await this.delay(3000);
                
            } catch (error) {
                console.error(`❌ 任务 ${i + 1} 执行异常:`, error);
                results.push({
                    task: task,
                    success: false,
                    error: error.message
                });
            }
        }
        
        const successCount = results.filter(r => r.success).length;
        console.log(`🎉 任务执行完成，成功: ${successCount}/${this.tasks.length}`);
        
        return results;
    }
    
    /**
     * 点击任务
     */
    async clickTask(task) {
        console.log(`👆 点击任务: ${task.name}`);
        
        const timestamp = Date.now();
        const payload = {
            giveawayId: this.giveawayId,
            taskId: task.id,
            walletAddress: this.walletAddress,
            chainId: this.chainId,
            platform: task.platform,
            bizType: 1
        };
        
        try {
            const response = await fetch(`${this.baseUrl}/giveaway/clickTask?t=${timestamp}`, {
                method: 'POST',
                headers: this.generateHeaders(timestamp),
                body: JSON.stringify(payload)
            });
            
            const result = await response.json();
            console.log(`📡 点击任务响应:`, result);
            
            return {
                success: result.code === 0,
                data: result
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 检查任务状态
     */
    async checkTaskStatus(task) {
        console.log(`🔍 检查任务状态: ${task.name}`);
        
        const timestamp = Date.now();
        const payload = {
            giveawayId: this.giveawayId,
            taskId: task.id,
            walletAddress: this.walletAddress,
            chainId: this.chainId,
            platform: task.platform
        };
        
        try {
            const response = await fetch(`${this.baseUrl}/giveaway/task/check?t=${timestamp}`, {
                method: 'POST',
                headers: this.generateHeaders(timestamp),
                body: JSON.stringify(payload)
            });
            
            const result = await response.json();
            console.log(`📡 任务状态响应:`, result);
            
            return {
                success: result.code === 0,
                data: result,
                completed: result.data?.completed || false
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 最终验证
     */
    async verifyCompletion() {
        console.log('✅ 执行最终验证...');
        
        const timestamp = Date.now();
        const payload = {
            giveawayId: this.giveawayId,
            walletAddress: this.walletAddress,
            chainId: this.chainId
        };
        
        try {
            const response = await fetch(`${this.baseUrl}/giveaway/verify?t=${timestamp}`, {
                method: 'POST',
                headers: this.generateHeaders(timestamp),
                body: JSON.stringify(payload)
            });
            
            const result = await response.json();
            console.log('📡 最终验证响应:', result);
            
            return {
                success: result.code === 0,
                data: result
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 生成授权码
     */
    generateAuthCode() {
        const userData = {
            userId: this.twitterAuth.userId,
            screenName: this.twitterAuth.screenName,
            csrfToken: this.twitterAuth.csrfToken,
            timestamp: Date.now(),
            nonce: this.generateNonce()
        };
        
        return btoa(JSON.stringify(userData));
    }
    
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 生成随机nonce
     */
    generateNonce() {
        return Math.random().toString(36).substring(2, 15) + 
               Math.random().toString(36).substring(2, 15);
    }
    
    // 以下是签名生成函数（需要根据实际情况调整）
    generateVerifySign(timestamp) {
        return 'D7d3aWXr/pUdhKwRd0G0CgF4q2c2nUuvbC+rKH2frRc=';
    }
    
    generateVerifyToken() {
        return '6949b459-de9d-4953-a692-993a02e39aad';
    }
    
    generateFpToken() {
        return '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    }
    
    generateFpTokenSignature() {
        return this.generateNonce();
    }
    
    generateIdGroup() {
        return '2140143310648640001-c-42';
    }
    
    generateDeviceToken() {
        return '55339c33-d6d7-4bbb-ab6a-83870d73f080';
    }
}

// 快速启动函数
window.startOKXJasperVaultAutomation = async function(walletAddress) {
    console.log('🚀 启动OKX Jasper Vault自动化...');
    
    // 使用提取的Twitter tokens
    const twitterTokens = {
        cookies: {
            ct0: "3d339fd2a7aded55f9b891bdeceaee6d7ad5370ed6b791afcdff7e3044aed7be5c79068943a6a80241843c71dca1bf0b38a3626658d58142922605f2534d79218f5d2a6b19d7bbd72d76bec4dac963cb",
            twid: "u%3D709003675"
        },
        userInfo: {
            method_3: "Follow @dsqqzx"
        }
    };
    
    const automation = new OKXJasperVaultAutomation(twitterTokens, walletAddress);
    const result = await automation.execute();
    
    if (result.success) {
        console.log('🎉 自动化执行成功！');
        console.log('📊 结果详情:', result);
    } else {
        console.log('❌ 自动化执行失败:', result.error);
    }
    
    return result;
};

console.log('🚀 OKX Jasper Vault 完整自动化脚本已加载');
console.log('📖 使用方法:');
console.log('  await startOKXJasperVaultAutomation("0x你的钱包地址");');
