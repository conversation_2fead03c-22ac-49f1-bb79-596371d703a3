# OKX钱包自动连接脚本使用指南

## 📋 概述
这是一个基于Puppeteer的无头浏览器脚本，可以自动连接OKX钱包到Web3应用。支持使用私钥自动化钱包连接流程。

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install puppeteer ethers
```

### 2. 配置钱包信息
编辑 `wallet_config.json` 文件，添加你的钱包私钥和地址：

```json
{
  "wallets": [
    {
      "name": "我的钱包",
      "privateKey": "0x你的私钥",
      "address": "0x你的钱包地址",
      "enabled": true
    }
  ]
}
```

### 3. 运行脚本
```bash
node headless_wallet_connect.js
```

## 🔧 配置选项

### 浏览器配置
- `headless`: 是否使用无头模式 (true/false)
- `devtools`: 是否打开开发者工具 (true/false)
- `slowMo`: 操作延迟时间 (毫秒)
- `timeout`: 超时时间 (毫秒)

### 钱包配置
- `privateKey`: 钱包私钥 (必需)
- `address`: 钱包地址 (必需)
- `enabled`: 是否启用此钱包

## 📝 使用示例

### 基本使用
```javascript
const { HeadlessWalletConnect } = require('./headless_wallet_connect');

async function connectWallet() {
    const connector = new HeadlessWalletConnect({
        headless: true,  // 无头模式
        slowMo: 100     // 100ms延迟
    });
    
    try {
        const result = await connector.connectWallet(
            'YOUR_PRIVATE_KEY',
            'YOUR_WALLET_ADDRESS', 
            'https://web3.okx.com/zh-hans/giveaway/jaspervault'
        );
        
        console.log('连接成功:', result);
    } finally {
        await connector.close();
    }
}

connectWallet();
```

### 批量连接多个钱包
```javascript
const config = require('./wallet_config.json');

async function connectMultipleWallets() {
    for (const wallet of config.wallets) {
        if (!wallet.enabled) continue;
        
        const connector = new HeadlessWalletConnect(config.browser);
        
        try {
            console.log(`连接钱包: ${wallet.name}`);
            await connector.connectWallet(
                wallet.privateKey,
                wallet.address,
                config.targets[0].url
            );
            
            // 截图保存结果
            await connector.screenshot(`${wallet.name}_result.png`);
            
        } catch (error) {
            console.error(`钱包 ${wallet.name} 连接失败:`, error);
        } finally {
            await connector.close();
        }
        
        // 钱包之间的延迟
        await new Promise(resolve => setTimeout(resolve, 5000));
    }
}
```

## 🔍 工作原理

### 1. 钱包模拟
脚本会注入一个模拟的OKX钱包对象到页面中：
- 模拟 `window.okxwallet` 和 `window.ethereum`
- 处理钱包连接请求
- 模拟签名操作

### 2. 自动化流程
1. 启动无头浏览器
2. 注入钱包模拟器
3. 导航到目标页面
4. 自动点击连接钱包按钮
5. 选择OKX钱包
6. 完成连接

### 3. 错误处理
- 自动重试机制
- 超时处理
- 详细的日志输出

## ⚠️ 安全注意事项

### 私钥安全
- **永远不要**将私钥提交到版本控制系统
- 使用环境变量存储敏感信息
- 定期轮换钱包私钥

### 使用建议
- 使用专门的测试钱包
- 不要在主钱包上运行自动化脚本
- 监控钱包活动和余额变化

## 🛠️ 高级配置

### 自定义选择器
如果目标网站的按钮选择器发生变化，可以修改脚本中的选择器：

```javascript
const buttonSelectors = [
    'button:contains("连接 OKX Wallet 并参与活动")',
    'button:contains("连接钱包")',
    '[data-testid="connect-wallet"]',
    // 添加新的选择器
];
```

### 添加新的目标网站
在 `wallet_config.json` 中添加新的目标：

```json
{
  "targets": [
    {
      "name": "新项目",
      "url": "https://example.com/connect",
      "enabled": true
    }
  ]
}
```

## 🐛 故障排除

### 常见问题

1. **连接按钮找不到**
   - 检查页面是否完全加载
   - 更新按钮选择器
   - 增加等待时间

2. **钱包选择失败**
   - 确认OKX钱包选项存在
   - 检查弹窗是否正确显示
   - 调整选择器

3. **连接超时**
   - 增加超时时间
   - 检查网络连接
   - 验证钱包地址格式

### 调试模式
设置 `headless: false` 和 `devtools: true` 来观察浏览器行为：

```javascript
const connector = new HeadlessWalletConnect({
    headless: false,
    devtools: true
});
```

## 📊 监控和日志

脚本会输出详细的执行日志：
- 🚀 启动信息
- 🔍 查找元素
- ✅ 成功操作
- ❌ 错误信息
- 📸 截图保存

## 🔄 更新和维护

### 定期检查
- 目标网站UI变化
- 选择器更新
- 新的反爬虫机制

### 版本控制
- 记录配置变更
- 备份工作配置
- 测试新版本

## 📞 支持

如果遇到问题：
1. 检查日志输出
2. 验证配置文件
3. 测试网络连接
4. 更新依赖包

## 🎯 下一步计划

- [ ] 支持更多钱包类型
- [ ] 添加交易自动化
- [ ] 集成代理支持
- [ ] 添加GUI界面
